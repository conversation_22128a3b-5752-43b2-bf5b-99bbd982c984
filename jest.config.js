module.exports = {
    preset: 'ts-jest',
    testEnvironment: 'node',
    testMatch: ['**/*.spec.ts'],
    // Ignore test files in certain directories
    testPathIgnorePatterns: ['/node_modules/', '/dist/'],
    // Setup file to run before tests
    setupFiles: ['./jest.setup.js'],

    // Coverage configuration
    collectCoverage: true, // Enable code coverage collection
    coverageDirectory: 'coverage', // Directory to store coverage reports
    coverageReporters: ['lcov', 'text', 'html'], // Coverage report formats

    // Test coverage threshold configuration
    coverageThreshold: {
        global: {
        branches: 80,
        functions: 80,
        lines: 80,
        statements: 80,
        },
    },
    testTimeout: 10000,

    // Clear mocks before each test run
    clearMocks: true,

    // Maximum number of lines Jest should print for a stack trace
    maxWorkers: '50%',
    verbose: true,
};
