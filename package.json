{"name": "midori", "version": "0.0.1", "private": true, "main": "build/app.js", "scripts": {"build": "rimraf ./build && tsc", "start:dev": "npx nodemon", "start": "npm run build && node --inspect=9229 build/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:debug": "node --inspect-brk ./node_modules/jest/bin/jest.js", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm migration:generate -- -d src/configs/datasource.config.ts", "migration:run": "npm run typeorm migration:run -- -d src/configs/datasource.config.ts", "migration:revert": "npm run typeorm migration:revert -- -d src/configs/datasource.config.ts"}, "dependencies": {"@types/csrf": "^3.1.3", "ajv": "^6.12.6", "audit": "^0.0.6", "awilix": "^12.0.5", "awilix-express": "^9.0.2", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "class-sanitizer": "^1.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "class-validator-jsonschema": "^5.0.1", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "csrf-csrf": "^3.1.0", "dayjs": "^1.11.13", "debug": "~2.6.9", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "flatpickr": "^4.6.13", "fs": "^0.0.1-security", "graphile-worker": "^0.16.6", "hbs": "^4.2.0", "http-errors": "~1.6.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "madge": "^8.0.0", "match-sorter": "^6.3.1", "morgan": "~1.9.1", "node-cron": "^3.0.3", "passport": "^0.7.0", "passport-cookie": "^1.0.9", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "pg": "^8.11.3", "pg-boss": "^7.4.0", "pg-format": "^1.0.4", "prettyjson": "^1.2.5", "rimraf": "^5.0.5", "routing-controllers": "^0.11.2", "routing-controllers-openapi": "^5.0.0", "rrule": "^2.8.1", "sort-by": "^0.0.2", "swagger-autogen": "^2.23.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.20", "uuid": "^11.0.4", "winston": "^3.12.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.18.1", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.17.0", "@types/morgan": "^1.9.9", "@types/node": "^20.11.24", "@types/passport": "^1.0.16", "@types/passport-jwt": "^4.0.0", "@types/passport-local": "^1.0.38", "@types/pg": "^8.10.9", "@types/pg-format": "^1.0.5", "@types/prettyjson": "^0.0.33", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.7", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.14", "file-loader": "^6.2.0", "jest": "^29.7.0", "kill-port": "^2.0.1", "nodemon": "^3.0.2", "prettier": "^3.0.0", "supertest": "^7.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}