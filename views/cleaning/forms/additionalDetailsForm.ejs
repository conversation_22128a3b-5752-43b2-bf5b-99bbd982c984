<div class="form-section">
    <!---Availability Schedule-->
    <div class="form-group">
        <h3>Additional Details</h3>
        <div class="form-row">
            <% (additionalDetails ?? []).forEach(function(additionalDetail, index) { %>
                <div class="form-field" hx-each="additionalDetail in additionalDetails">
                    <label for="<%= additionalDetail.id %>"><%= additionalDetail.name %>:</label>
                    <select 
                        id="<%= additionalDetail.id %>"
                        name="additionalDetails[]"
                        class="material-ui-select"
                        hx-on:change="calculatePrice()">
                        <option value="">Select Your Answer</option>
                        <% additionalDetail.choices.forEach(function(choice, index) { %>
                            <option value='<%= choice.price %>'>
                                <%= choice.name %>
                            </option>
                        <% }) %>
                    </select>
                </div>
            <% }); %>
        </div>
    </div>
</div>