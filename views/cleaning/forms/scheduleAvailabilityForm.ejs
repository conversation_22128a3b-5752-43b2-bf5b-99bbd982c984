
<div class="form-section">
    <!---Availability Schedule-->
    <div class="form-group">
        <h3>When would you like us to come?</h3>
        <div class="form-row">
            <div class="form-field">
                <label for="service-date">Service date:</label>
                <input type="date" 
                        id="service-date" 
                        name="serviceDate" 
                        required 
                        class="material-ui-input" 
                        hx-target="#arrival-time" 
                        hx-trigger="change">
            </div>
            <div class="form-field">
                <label for="arrival-time">Arrival time:</label>
                <select id="arrival-time" 
                        name="arrivalTime" 
                        class="material-ui-select">
                    <option value="">Select Time</option>
                    <% 
                    // Generate times from 6:00 AM to 9:00 PM
                    for(let hour = 6; hour < 21; hour++) {
                        const hourPad = hour.toString().padStart(2, '0');
                        // For each hour, create two slots (:00 and :30)
                        const amPm1 = hour < 12 ? 'AM' : 'PM';
                        const displayHour1 = hour > 12 ? hour - 12 : hour;
                        const displayHourStr1 = displayHour1.toString().padStart(2, '0');
                    %>
                        <option value="<%= `${hourPad}:00` %>">
                            <%= `${displayHourStr1}:00 ${amPm1}` %>
                        </option>
                        <option value="<%= `${hourPad}:30` %>">
                            <%= `${displayHourStr1}:30 ${amPm1}` %>
                        </option>
                    <% } %>
                </select>
                <small>Estimated Time of Arrival (Approximate)</small>
            </div>
        </div>
    </div>
</div>