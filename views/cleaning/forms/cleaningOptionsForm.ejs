<!-- Service Details Section -->
<div class="form-section">
    <div class="form-group">
        <h3>Service Details</h3>
    <!-- Dynamic Service Details -->
        <div class="form-row">
            <% (serviceDetails ?? []).forEach(function(detail, index) { %>
                <div class="form-field" hx-each="detail in serviceDetails">
                    <label for="<%= detail.id %>"><%= detail.name %>:</label>
                    <select 
                        id="<%= detail.id %>"
                        name="serviceDetails[]"
                        class="material-ui-select"
                        data-price-per-unit="<%= detail.price %>"
                        hx-on:change="calculatePrice()">
                        <option value="">Select Amount</option>
                        <% for(let i = 1; i <= detail.available_amount; i++) { %>
                            <option value='{"id": "<%= detail.id %>", "amount": <%= i %>}'>
                                <%= i %> <%= detail.name %><%= i !== 1 ? 's' : '' %>
                            </option>
                        <% } %>
                    </select>
                </div>
            <% }); %>
        </div>

    <!-- Service Area and Type Row -->
        <div class="form-row">
            <div class="form-field">
                <label for="serviceArea">Service Area (sq ft)</label>
                <select 
                    name="serviceArea" 
                    class="material-ui-select" 
                    hx-on:change="calculatePrice()">
                    <option value="">Select Service Area</option>
                    <% (serviceAreas ?? []).forEach(function(area) { %>
                        <option 
                            value="<%= area.id %>"
                            data-area-price="<%= area.price %>"
                            data-sqftfrom="<%= area.sqFtFrom %>"
                            data-sqftto="<%= area.sqFtTo %>">
                            <%= area.sqFtFrom %> - <%= area.sqFtTo %>
                        </option>
                    <% }); %>
                </select>
            </div>

            <div class="form-field">
                <label for="serviceType">Service Type</label>
                <select 
                    name="serviceType" 
                    class="material-ui-select" 
                    hx-on:change="calculatePrice()">
                    <option value="">Select Service Type</option>
                    <% (typeOfServices ?? []).forEach(function(service) { %>
                        <option 
                            value="<%= service.id %>"
                            data-service-type-name="<%= service.name %>"
                            data-service-price="<%= service.price %>">
                            <%= service.name %>
                        </option>
                    <% }); %>
                </select>
            </div>
        </div>
    </div>
</div>


<!---Service Frequency-->
<!-- <div class="form-group">
<h2>Select Service Frequency</h2>
<div class="form-row">
    <% (serviceFrequencies ?? []).forEach(frequency => { %>
        <div>
            <input type="radio" 
                    id="<%= frequency.id %>"
                    name="frequency"
                    value="<%= frequency.name %>"
                    class="frequency-input">
            <label for="<%= frequency.id %>" class="frequency-label">
                <%= frequency.name %>
            </label>
        </div>
    <% }); %>
</div>
</div> -->
