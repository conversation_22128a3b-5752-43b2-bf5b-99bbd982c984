<div class="form-section">
    <div class="form-group">
      <h3>Contact Details</h3>
      <div class="form-row">
        <div class="form-field">
          <label for="first-name">First name:</label>
          <input type="text" id="first-name" name="client[firstName]" required class="material-ui-input" placeholder="First name*">
        </div>
        <div class="form-field">
          <label for="last-name">Last name:</label>
          <input type="text" id="last-name" name="client[lastName]" required class="material-ui-input" placeholder="Last name*">
        </div>
      </div>
      <div class="form-row">
        <div class="form-field">
          <label for="email">Email:</label>
          <input type="email" id="email" name="client[email]" required class="material-ui-input" placeholder="Email*">
        </div>
        <div class="form-field">
          <label for="phone-number">Phone number:</label>
          <input type="tel" id="phone-number" name="client[phoneNumber]" required class="material-ui-input" placeholder="Phone number*">
        </div>
      </div>
    </div>
    <div class="form-group">
      <h3>Service Address</h3>
      <div class="form-row">
        <div class="form-field">
          <label for="address-line-1">Address line 1:</label>
          <input type="text" id="address-line-1" name="client[addressInfo][street]" required class="material-ui-input" placeholder="Address line 1*">
        </div>
      </div>
      <div class="form-row">
        <div class="form-field">
          <label for="city">City:</label>
          <input type="text" id="city" name="client[addressInfo][city]" required class="material-ui-input" placeholder="City*">
        </div>
        <div class="form-field">
          <label for="province">Select province:</label>
          <input type="text" id="province" name="client[addressInfo][state]" required class="material-ui-input" placeholder="Select province*">
        </div>
        <div class="form-field">
          <label for="postal-code">Postal code:</label>
          <input type="text" id="postal-code" name="client[addressInfo][postalCode]" required class="material-ui-input" placeholder="Postal code*">
        </div>
      </div>
    </div>
</div>
  