<div class="form-section">
    <!---Availability Schedule-->
    <div class="form-group">
        <h3>Available Extras</h3>
        <div class="form-row">
            <% (jobExtras ?? []).forEach(function(extra, index) { %>
                <div class="form-field" hx-each="extra in jobExtras">
                    <label for="<%= extra.id %>"><%= extra.name %> (+ $<%= extra.price %>):</label>
                    <select 
                        id="<%= extra.id %>"
                        name="jobExtras[]"
                        class="material-ui-select"
                        data-price-per-extra="<%= extra.price %>"
                        hx-on:change="calculatePrice()">
                        <option value="">Select Amount</option>
                        <% for(let i = 1; i <= extra.availableAmount; i++) { %>
                            <option value='{"id": "<%= extra.id %>", "amount": <%= i %>}'>
                                <%= i %> <%= extra.name %><%= i !== 1 ? 's' : '' %>
                            </option>
                        <% } %>
                    </select>
                </div>
            <% }); %>
        </div>
    </div>
</div>