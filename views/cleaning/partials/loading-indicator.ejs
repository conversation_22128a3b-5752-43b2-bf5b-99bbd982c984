<style>
    .loading-indicator {
      display: flex;
      align-items: center;
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      background-color: #f8f9fa;
    }
    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top-color: #007bff;
      animation: spin 1s ease-in-out infinite;
      margin-right: 10px;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
</style>

<div id="loading-indicator" class="htmx-indicator" style="display:none;">
<div class="loading-spinner"></div>
    <span>Loading, please wait...</span>
</div>