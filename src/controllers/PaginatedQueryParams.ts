import { Type } from "class-transformer";
import { <PERSON><PERSON>nt, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";

export class PaginatedQueryParams{
    @IsOptional()
    @Type(() => Number)
    @IsInt({ message: 'limit must be an integer' })
    @Min(1, { message: 'limit must be at least 1' })
    @Max(100, { message: 'limit must not exceed 100' })
    limit?: number;

    @IsOptional()
    @Type(() => Number)
    @IsInt({ message: 'offset must be an integer' })
    @Min(0, { message: 'offset must be non-negative' })
    @Max(100, { message: 'offset must not exceed 100' })
    offset?: number
}