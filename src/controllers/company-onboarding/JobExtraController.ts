import { <PERSON><PERSON><PERSON><PERSON>, JsonController, Get, CurrentUser, QueryParams, Param, Post, Body, Patch, Delete } from "routing-controllers";
import { ResponseSchema } from "routing-controllers-openapi";
import { CreateJobExtraDto, PaginatedResponseDto, ResponseJobExtraDto, UpdateJobExtraDto } from "../../dto/v1";
import { AuthenticationMiddleware } from "../../middlewares";
import { UserRequestObj } from "../../types";
import { Logging } from "../../util";
import { PaginatedQueryParams } from "../PaginatedQueryParams";
import { JobExtraService } from "../../services";

@UseBefore(AuthenticationMiddleware)
@JsonController('/v1/companies/extras')
export class JobExtraController {

    constructor(
        private readonly logger: Logging = new Logging(),
        private jobExtraService: JobExtraService = new JobExtraService()
    ){}

    @Get('/')
    @ResponseSchema(PaginatedResponseDto<ResponseJobExtraDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Retrieve all residential cleaning extras for company'
    })
    async getResidentialCleaningExtrasPerCompanyId (
        @CurrentUser() user: UserRequestObj,
        @QueryParams() params: PaginatedQueryParams,
    ): Promise<PaginatedResponseDto<ResponseJobExtraDto>> {
        this.logger.info(`Get all extras for following company: ${user.companyId}`)
        const {extras, total} = await this.jobExtraService.findResidentialCleaningExtrasPerCompanyId(user.companyId, params.offset, params.limit);
        return new PaginatedResponseDto<ResponseJobExtraDto>("Retrieved residential cleaning extra successfully", extras, params.offset, params.limit, total)
    }

    @Get('/:id')
    @ResponseSchema(PaginatedResponseDto<ResponseJobExtraDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Retrieve a residential cleaning extra per extraId'
    })
    async getResidentialCleaningExtraPerExtraId (
        @CurrentUser() user: UserRequestObj,
        @Param('id') id: string
    ): Promise<PaginatedResponseDto<ResponseJobExtraDto>> {
        this.logger.info(`Get an extra for following extraId: ${id}`)
        const response = await this.jobExtraService.findResidentialCleaningExtraPerExtraId(id, user.companyId);
        return new PaginatedResponseDto<ResponseJobExtraDto>("Retrieved residential cleaning extra successfully", response)
    }


    @Post('/')
    @ResponseSchema(PaginatedResponseDto<ResponseJobExtraDto>, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Create a residential cleaning extra for company'
    })
    async createResidentialCleaningExtraPerCompanyId (
        @CurrentUser() user: UserRequestObj,
        @Body() body: CreateJobExtraDto,
    ): Promise<PaginatedResponseDto<ResponseJobExtraDto>> {
        this.logger.info(`Upsert extra for following company: ${user.companyId}`)
        const response = await this.jobExtraService.createResidentialCleaningExtraPerCompanyId(body, user.companyId)
        return new PaginatedResponseDto<ResponseJobExtraDto>("Created new residential cleaning extra successfully", response);

    }

    @Patch('/:id')
    @ResponseSchema(PaginatedResponseDto<ResponseJobExtraDto>, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Update a residential cleaning extra for company'
    })
    async updateResidentialCleaningExtraPerCompanyId (
        @CurrentUser() user: UserRequestObj,
        @Body() body: UpdateJobExtraDto,
        @Param('id') id: string,
    ): Promise<PaginatedResponseDto<ResponseJobExtraDto>> {
        this.logger.info(`Upsert extra for following company: ${user.companyId}`)
        const response = await this.jobExtraService.updateResidentialCleaningExtraPerCompanyId(body, id, user.companyId)
        return new PaginatedResponseDto<ResponseJobExtraDto>("Updated new residential cleaning extra successfully", response);
    }

    @Delete('/:id')
    @ResponseSchema(PaginatedResponseDto<ResponseJobExtraDto>, {
        contentType: 'application/json',
        statusCode: 204,
        description: 'Delete job per job id'
    })
    async deleteResidentialCleaningExtraPerExtraId  (
        @CurrentUser() user: UserRequestObj,
        @Param('id') id: string
    ): Promise<PaginatedResponseDto<ResponseJobExtraDto>>  {
        this.logger.info(`Delete extra with following id: ${id}`)
        await this.jobExtraService.deleteResidentialCleaningExtrasPerCompanyId(id, user.companyId);
        return new PaginatedResponseDto<ResponseJobExtraDto>(`Successfully deleted job ${id}`);
    }

}