import { UseBefore, Post, JsonController, Body, CurrentUser, Get, QueryParams, Param, Put, Patch, Delete } from "routing-controllers";
import { ResponseSchema } from "routing-controllers-openapi";
import { CreateServiceFrequencyDto, PaginatedResponseDto, ResponseServiceFrequencyDto, UpdateServiceFrequencyDto, } from "../../dto/v1";
import { AuthenticationMiddleware } from "../../middlewares";
import { ServiceFrequencyService } from "../../services";
import { UserRequestObj } from "../../types";
import { CompanySettingsQueryParams } from "./CompanySettingsQueryParams";

@JsonController('/v1/companies/service-frequencies')
export class ServiceFrequencyController{ 

    constructor(
        private readonly serviceFrequencyService: ServiceFrequencyService = new ServiceFrequencyService()
    ){}

    @UseBefore(AuthenticationMiddleware)
    @Get()
    @ResponseSchema(PaginatedResponseDto<ResponseServiceFrequencyDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Create service area using company id'
    })
    async getAllServiceFrequenciesForCompany(
        @CurrentUser() user: UserRequestObj,
        @QueryParams() params: CompanySettingsQueryParams,
    ): Promise<PaginatedResponseDto<ResponseServiceFrequencyDto>> {
        const {result, total} = await this.serviceFrequencyService.findServiceFrequencyForCompany({
            companyId: user.companyId, offset:params.offset, limit:params.limit, isActive: params?.isActive
        });
        return new PaginatedResponseDto<ResponseServiceFrequencyDto>(`Retrieved successfully all service frequencies for company`, result, params.offset, params.limit, total);
    }

    @UseBefore(AuthenticationMiddleware)
    @Get('/:id')
    @ResponseSchema(PaginatedResponseDto<ResponseServiceFrequencyDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Create service area using company id'
    })
    async getServiceFrequencyForCompanyById(
        @CurrentUser() user: UserRequestObj,
        @Param('id') id: string, 
        @QueryParams() params: CompanySettingsQueryParams,
    ): Promise<PaginatedResponseDto<ResponseServiceFrequencyDto>> {
        const result = await this.serviceFrequencyService.findServiceFrequencyForCompanyById({id: id, companyId: user.companyId, isActive: params?.isActive })
        return new PaginatedResponseDto<ResponseServiceFrequencyDto>(`Retrieved successfully service frequency with id ${id}`, result);
    }

    @UseBefore(AuthenticationMiddleware)
    @Post()
    @ResponseSchema(PaginatedResponseDto<ResponseServiceFrequencyDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Create service area using company id'
    })
    async createServiceFrequenciesForCompany(
        @CurrentUser() user: UserRequestObj,
        @Body() body: CreateServiceFrequencyDto
    ): Promise<PaginatedResponseDto<ResponseServiceFrequencyDto>> {
        const result = await this.serviceFrequencyService.createServiceFrequencyForCompany(body, user.companyId);
        return new PaginatedResponseDto<ResponseServiceFrequencyDto>(`Created successfully service frequency for company`, result);
    }

    @UseBefore(AuthenticationMiddleware)
    @Patch('/:id')
    @ResponseSchema(PaginatedResponseDto<ResponseServiceFrequencyDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Create service area using company id'
    })
    async updateServiceFrequencyForCompanyById(
        @CurrentUser() user: UserRequestObj,
        @Param('id') id: string,
        @Body() body: UpdateServiceFrequencyDto
    ): Promise<PaginatedResponseDto<ResponseServiceFrequencyDto>>{
        const result = await this.serviceFrequencyService.updateServiceFrequencyForCompany(body, id, user.companyId);
        return new PaginatedResponseDto<ResponseServiceFrequencyDto>(`Updated successfully service frequency with id ${id}`, result);
    }

    @UseBefore(AuthenticationMiddleware)
    @Delete('/:id')
    @ResponseSchema(PaginatedResponseDto<ResponseServiceFrequencyDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Create service area using company id'
    })
    async deleteServiceFrequencyForCompanyById(
        @CurrentUser() user: UserRequestObj,
        @Param('id') id: string
    ): Promise<PaginatedResponseDto<ResponseServiceFrequencyDto>> {
        await this.serviceFrequencyService.deleteServiceFrequencyForCompany(id, user.companyId);
        return new PaginatedResponseDto<ResponseServiceFrequencyDto>(`Deleted successfully service frequency with id ${id}`);

    }
}