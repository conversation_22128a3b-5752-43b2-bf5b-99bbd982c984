import { Body, CurrentUser, Delete, Get, JsonController, Param, Patch, Post, QueryParams, UseBefore } from "routing-controllers";
import { ServiceAreaService } from "../../services";
import { ResponseSchema } from "routing-controllers-openapi";
import { CreateServiceAreaDto, PaginatedResponseDto, ResponseServiceAreaDto, UpdateServiceAreaDto } from "../../dto/v1";
import { AuthenticationMiddleware } from "../../middlewares";
import { UserRequestObj } from "../../types";
import { PaginatedQueryParams } from "../PaginatedQueryParams";

@UseBefore(AuthenticationMiddleware)
@JsonController('/v1/companies/service-area')
export class ServiceAreaController {

    constructor(
        private readonly serviceAreaService: ServiceAreaService = new ServiceAreaService()
    ){}

    @Post()
    @ResponseSchema(PaginatedResponseDto<ResponseServiceAreaDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Create service area using company id'
    })
    async createNewServiceAreaByCompanyId(
        @CurrentUser() user: UserRequestObj,
        @Body() body: CreateServiceAreaDto
    ): Promise<PaginatedResponseDto<ResponseServiceAreaDto>> {
        const result = await this.serviceAreaService.createServiceArea(body, user.companyId);
        return new PaginatedResponseDto<ResponseServiceAreaDto>("New Service Area Successfully Created", result);
    }

    @Patch('/:serviceAreaId')
    @ResponseSchema(PaginatedResponseDto<ResponseServiceAreaDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Update service area using service area id'
    })
    async updateServiceAreaById(
        @CurrentUser() user: UserRequestObj,
        @Param('serviceAreaId') serviceAreaId: string,
        @Body() body: UpdateServiceAreaDto
    ): Promise<PaginatedResponseDto<ResponseServiceAreaDto>> {
        const result = await this.serviceAreaService.updateServiceAreaById(body, serviceAreaId, user.companyId);
        return new PaginatedResponseDto<ResponseServiceAreaDto>("Service Area Updated Successfully", result);
    }

    @Get('/:serviceAreaId')
    @ResponseSchema(PaginatedResponseDto<ResponseServiceAreaDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Find service area by service area id'
    })
    async getServiceAreaById(
        @Param('serviceAreaId') serviceAreaId: string,
        @CurrentUser() user: UserRequestObj,
    ): Promise<PaginatedResponseDto<ResponseServiceAreaDto>>{
        const result = await this.serviceAreaService.findServiceAreaById(serviceAreaId, user.companyId);
        return new PaginatedResponseDto<ResponseServiceAreaDto>(`Retrieve service area by id:${serviceAreaId}`, result);
    }


    @Get()
    @ResponseSchema(PaginatedResponseDto<ResponseServiceAreaDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Find all service area by company id'
    })
    async getAllServiceAreasForCompany(
        @CurrentUser() user: UserRequestObj,
        @QueryParams() params: PaginatedQueryParams,
    ):Promise<PaginatedResponseDto<ResponseServiceAreaDto>> {
        const {result, total} = await this.serviceAreaService.findAllServiceAreasByCompanyId(user.companyId, params.offset, params.limit);
        return new PaginatedResponseDto<ResponseServiceAreaDto>(`Retrieve All Service Areas For Company`, result, params.offset, params.limit, total);
    }

    @Delete('/:serviceAreaId')
    @ResponseSchema(PaginatedResponseDto<ResponseServiceAreaDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Delete service area by service area id'
    })
    async deleteServiceAreaById(
        @CurrentUser() user: UserRequestObj,
        @Param('serviceAreaId') serviceAreaId: string
    ): Promise<PaginatedResponseDto<ResponseServiceAreaDto>> {
        await this.serviceAreaService.deleteServiceAreaById(serviceAreaId, user.companyId);
        return new PaginatedResponseDto<ResponseServiceAreaDto>('Service Area Deleted Successfully');
    }
}