import { Body, CurrentUser, Delete, Get, JsonController, Param, Patch, Post, QueryParams, UseBefore } from "routing-controllers";
import { ServicePropertySetupService } from "../../services";
import { CreateServicePropertySetupDto, PaginatedResponseDto, ResponseServicePropertySetupDto, UpdateServicePropertySetupDto } from "../../dto/v1";
import { ResponseSchema } from "routing-controllers-openapi";
import { UserRequestObj } from "../../types";
import { AuthenticationMiddleware } from "../../middlewares";
import { PaginatedQueryParams } from "../PaginatedQueryParams";

@JsonController('/v1/company/service-property')
export class ServicePropertySetupController {
    constructor(
        private readonly servicePropertySetupService: ServicePropertySetupService = new ServicePropertySetupService()
    ){}

    @UseBefore(AuthenticationMiddleware)
    @Get()
    @ResponseSchema(PaginatedResponseDto<ResponseServicePropertySetupDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Retrieve type of service using company id'
    })
    async getAllServicePropertySetupForCompany(
        @CurrentUser() user: UserRequestObj,
        @QueryParams() params: PaginatedQueryParams
    ): Promise<PaginatedResponseDto<ResponseServicePropertySetupDto>> {
        const {result, total} = await this.servicePropertySetupService.findAllServicePropertySetupForCompany(user.companyId, params.offset, params.limit);
        return new PaginatedResponseDto<ResponseServicePropertySetupDto>("Successfully return all service properties", result, params.offset, params.limit, total);
    }

    @UseBefore(AuthenticationMiddleware)
    @Get('/:servicePropertySetupId')
    @ResponseSchema(PaginatedResponseDto<ResponseServicePropertySetupDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Retrieve type of service using company id'
    })
    async getServicePropertySetupForCompanyById(
        @Param('servicePropertySetupId') servicePropertySetupId: string,
        @CurrentUser() user: UserRequestObj,
    ): Promise<PaginatedResponseDto<ResponseServicePropertySetupDto>> {
        const result = await this.servicePropertySetupService.findServicePropertySetupByID(servicePropertySetupId, user.companyId);
        return new PaginatedResponseDto<ResponseServicePropertySetupDto>(`Successfully return service property for id ${servicePropertySetupId}`, result);
    }


    @UseBefore(AuthenticationMiddleware)
    @Post()
    @ResponseSchema(PaginatedResponseDto<ResponseServicePropertySetupDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Retrieve type of service using company id'
    })
    async createServicePropertySetupForCompany(
        @CurrentUser() user: UserRequestObj,
        @Body() body: CreateServicePropertySetupDto
    ): Promise<PaginatedResponseDto<ResponseServicePropertySetupDto>>{
        const result = await this.servicePropertySetupService.createServicePropertySetup(body, user.companyId);
        return new PaginatedResponseDto<ResponseServicePropertySetupDto>(`Successfully created service property`, result);
    }

    @UseBefore(AuthenticationMiddleware)
    @Patch()
    @ResponseSchema(PaginatedResponseDto<ResponseServicePropertySetupDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Retrieve type of service using company id'
    })
    async updateServicePropertySetupForCompany(
        @CurrentUser() user: UserRequestObj,
        @Body() body: UpdateServicePropertySetupDto
    ): Promise<PaginatedResponseDto<ResponseServicePropertySetupDto>>{
        const result = await this.servicePropertySetupService.createServicePropertySetup(body, user.companyId);
        return new PaginatedResponseDto<ResponseServicePropertySetupDto>(`Successfully created service property`, result);
    }

    @UseBefore(AuthenticationMiddleware)
    @Delete('/:servicePropertySetupId')
    @ResponseSchema(PaginatedResponseDto<ResponseServicePropertySetupDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Retrieve type of service using company id'
    })
    async deletePropertySetupForCompanyById(
        @Param('servicePropertySetupId') servicePropertySetupId: string,
        @CurrentUser() user: UserRequestObj,
    ): Promise<PaginatedResponseDto<ResponseServicePropertySetupDto>> {
        await this.servicePropertySetupService.deleteServicePropertySetupById(servicePropertySetupId, user.companyId);
        return new PaginatedResponseDto<ResponseServicePropertySetupDto>(`Successfully deleted service property for id ${servicePropertySetupId}`);
    }
}