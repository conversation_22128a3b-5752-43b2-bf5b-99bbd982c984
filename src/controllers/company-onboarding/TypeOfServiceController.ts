import { Body, CurrentUser, Delete, Get, JsonController, Param, Patch, Post, QueryParams, UseBefore } from "routing-controllers";
import { AuthenticationMiddleware } from "../../middlewares";
import { ResponseSchema } from "routing-controllers-openapi";
import { UserRequestObj } from "../../types";
import { CreateTypeOfServiceDto, PaginatedResponseDto, ResponseTypeOfServiceDto, UpdateTypeOfServiceDto } from "../../dto/v1";
import { TypeOfServiceService } from "../../services";
import { PaginatedQueryParams } from "../PaginatedQueryParams";

@JsonController('/v1/companies/type-of-services')
export class TypeOfServiceController {

    constructor(        
        private readonly typeOfServiceService: TypeOfServiceService = new TypeOfServiceService()
    ){}

    @UseBefore(AuthenticationMiddleware)
    @Post()
    @ResponseSchema(PaginatedResponseDto<ResponseTypeOfServiceDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Create type of service using company id'
    })
    async createTypeOfServiceByCompanyId (
        @CurrentUser() user: UserRequestObj,
        @Body() body: CreateTypeOfServiceDto
    ): Promise<PaginatedResponseDto<ResponseTypeOfServiceDto>> {
        const result = await this.typeOfServiceService.createNewTypeOfService(user.companyId, body);
        return new PaginatedResponseDto("Successfully created new type of service", result);
    }

    @UseBefore(AuthenticationMiddleware)
    @Patch('/:typeOfServiceId')
    @ResponseSchema(PaginatedResponseDto<ResponseTypeOfServiceDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Update type of service using type of service id'
    })
    async updateTypeOfServiceForCompanyById(
        @Param('typeOfServiceId') typeOfServiceId: string,
        @CurrentUser() user: UserRequestObj,
        @Body() body: UpdateTypeOfServiceDto
    ): Promise<PaginatedResponseDto<ResponseTypeOfServiceDto>> {
        const result = await this.typeOfServiceService.updateTypeOfService(typeOfServiceId, body, user.companyId);
        return new PaginatedResponseDto("Successfully updated new type of service", result);

    }

    @UseBefore(AuthenticationMiddleware)
    @Get()
    @ResponseSchema(PaginatedResponseDto<ResponseTypeOfServiceDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Retrieve type of service using company id'
    })
    async getAllTypeOfServicesByCompanyId (
        @CurrentUser() user: UserRequestObj,
        @QueryParams() params: PaginatedQueryParams,
    ): Promise<PaginatedResponseDto<ResponseTypeOfServiceDto>> {
        const {result, total} = await this.typeOfServiceService.findAllTypeOfServicesForCompany(user.companyId, params.offset, params.limit);
        return new PaginatedResponseDto<ResponseTypeOfServiceDto>("Retrieved type of services successfully", result, params.offset, params.limit, total)
    }


    @UseBefore(AuthenticationMiddleware)
    @Get('/:typeOfServiceId')
    @ResponseSchema(PaginatedResponseDto<ResponseTypeOfServiceDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Retrieve type of service using type of service id'
    })
    async getTypeOfServiceForCompanyById (
        @CurrentUser() user: UserRequestObj,
        @Param('typeOfServiceId') typeOfServiceId: string,
    ): Promise<PaginatedResponseDto<ResponseTypeOfServiceDto>> {
        const result = await this.typeOfServiceService.findTypeOfServiceByServiceId(typeOfServiceId, user.companyId);
        return new PaginatedResponseDto<ResponseTypeOfServiceDto>("Retrieved type of service successfully", result)
    }

    @UseBefore(AuthenticationMiddleware)
    @Delete('/:typeOfServiceId')
    @ResponseSchema(PaginatedResponseDto<ResponseTypeOfServiceDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Delete type of service using by type of service id'
    })
    async deleteTypeOfServiceForCompanyById (
        @CurrentUser() user: UserRequestObj,        
        @Param('typeOfServiceId') typeOfServiceId: string
    ): Promise<PaginatedResponseDto<ResponseTypeOfServiceDto>> {
        await this.typeOfServiceService.deleteTypeOfServiceByServiceId(typeOfServiceId, user.companyId);
        return new PaginatedResponseDto<ResponseTypeOfServiceDto>(`Deleted type of service successfully for type of service id: ${typeOfServiceId}`);
    }
}