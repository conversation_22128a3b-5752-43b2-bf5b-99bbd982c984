import { <PERSON>sonController, UseBefore, Post, CurrentUser, QueryParams, Get, Param, Body, Patch, Delete } from "routing-controllers";
import { ResponseSchema } from "routing-controllers-openapi";
import { CreateAdditionalDetailsDto, PaginatedResponseDto, ResponseAdditionalDetailsDto, UpdateAdditionalDetailsDto } from "../../dto/v1";
import { AuthenticationMiddleware } from "../../middlewares";
import { UserRequestObj } from "../../types";
import { PaginatedQueryParams } from "../PaginatedQueryParams";
import { AdditionalDetailsService } from "../../services";

@JsonController('/v1/companies/additional-details')
export class AdditionalDetailsController {

    constructor(
        private readonly additionalDetailsService: AdditionalDetailsService = new AdditionalDetailsService()
    ){}

    @UseBefore(AuthenticationMiddleware)
    @Get()
    @ResponseSchema(PaginatedResponseDto<ResponseAdditionalDetailsDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Create service area using company id'
    })
    async getAllAdditionalDetailsForCompany(
        @CurrentUser() user: UserRequestObj,
        @QueryParams() params: PaginatedQueryParams,
    ): Promise<PaginatedResponseDto<ResponseAdditionalDetailsDto>> {
        const {result, total} =  await this.additionalDetailsService.findAllAdditionalDetailsForCompany(user.companyId, params.offset, params.limit);
        return new PaginatedResponseDto<ResponseAdditionalDetailsDto>("Retrieved successfully all additional details per company", result, params.offset, params.limit, total);
    }

    @UseBefore(AuthenticationMiddleware)
    @Get('/:additionalDetailId')
    @ResponseSchema(PaginatedResponseDto<ResponseAdditionalDetailsDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Create service area using company id'
    })
    async getAdditionalDetailForCompanyPerId(
        @Param('additionalDetailId') additionalDetailId: string,
        @CurrentUser() user: UserRequestObj,
    ): Promise<PaginatedResponseDto<ResponseAdditionalDetailsDto>> {
        const result = await this.additionalDetailsService.findAdditionalDetailForCompanyPerId(additionalDetailId, user.companyId);
        return new PaginatedResponseDto<ResponseAdditionalDetailsDto>(`Retrieved successfully additional details with id: ${additionalDetailId}`, result);
    }

    @UseBefore(AuthenticationMiddleware)
    @Post()
    @ResponseSchema(PaginatedResponseDto<ResponseAdditionalDetailsDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Create service area using company id'
    })
    async createAdditionalDetailsForCompany(
        @CurrentUser() user: UserRequestObj,
        @Body() body: CreateAdditionalDetailsDto
    ): Promise<PaginatedResponseDto<ResponseAdditionalDetailsDto>> {
        const result = await this.additionalDetailsService.createAdditionalDetailForCompany(body, user.companyId);
        return new PaginatedResponseDto<ResponseAdditionalDetailsDto>(`Created successfully additional details`, result);
    }

    @UseBefore(AuthenticationMiddleware)
    @Patch('/:additionalDetailId')
    @ResponseSchema(PaginatedResponseDto<ResponseAdditionalDetailsDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Create service area using company id'
    })
    async updateAdditionalDetailsForCompanyPerId(
        @Param('additionalDetailId') additionalDetailId: string,
        @CurrentUser() user: UserRequestObj,
        @Body() body: UpdateAdditionalDetailsDto
    ): Promise<PaginatedResponseDto<ResponseAdditionalDetailsDto>> {
        const result = await this.additionalDetailsService.updateAdditionalDetailForCompanyPerId(body, additionalDetailId, user.companyId);
        return new PaginatedResponseDto<ResponseAdditionalDetailsDto>(`Update successfully additional details with id: ${additionalDetailId}`, result);
    }

    @UseBefore(AuthenticationMiddleware)
    @Delete('/:additionalDetailId')
    @ResponseSchema(PaginatedResponseDto<ResponseAdditionalDetailsDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Create service area using company id'
    })
    async deleteAdditionalDetailsForCompanyPerId(
        @Param('additionalDetailId') additionalDetailId: string,
        @CurrentUser() user: UserRequestObj,
    ): Promise<PaginatedResponseDto<ResponseAdditionalDetailsDto>> {
        await this.additionalDetailsService.deleteAdditionalDetailForCompanyPerId(additionalDetailId, user.companyId);
        return new PaginatedResponseDto<ResponseAdditionalDetailsDto>(`Deleted successfully additional details with id: ${additionalDetailId}`);
    }
}