import { Response } from "express";
import { InvitationCodeGenerator } from "../util";
import { Body, JsonController, Post, Res } from 'routing-controllers';
import { ResponseSchema } from "routing-controllers-openapi";
import { InvitationCodeRequestDto, InvitationCodeResponseDto } from "../dto/v1";

@JsonController('/v1/invitation-code')
export default class InvitationCodeController {

    @Post()
    @ResponseSchema(InvitationCodeResponseDto, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Invitation Code Generator Successfully'
    })
    createInvitationCode(@Res() res: Response, @Body() body: InvitationCodeRequestDto) {
        const response = InvitationCodeGenerator.generateInvitationCode(body.role);

        res.status(200).json({
            message:'Code created',
            data: response
        });
    }

}
