import { ServiceFrequencyService } from './../../services/company-setup/ServiceFrequencyService';
import { CurrentUser, Get, JsonController, QueryParams, Res } from 'routing-controllers';
import { AdditionalDetailsService, JobExtraService, ServiceAreaService, ServicePropertySetupService, TypeOfServiceService } from '../../services';
import { Response } from 'express';
import { UseAuthentication } from '../../middlewares';
import { AuthenticationType, PartnerFormUser } from '../../types';
import { BookingFormQueryParams } from './BookingFormQueryParams';


@JsonController('/v1/widget')
export class BookingFormController {
    constructor(
        private readonly serviceAreaService: ServiceAreaService = new ServiceAreaService(),
        private readonly servicePropertySetupService: ServicePropertySetupService = new ServicePropertySetupService(),
        private readonly typeOfServiceService: TypeOfServiceService = new TypeOfServiceService(),
        private readonly serviceFrequencyService: ServiceFrequencyService = new ServiceFrequencyService(),
        private readonly jobExtraService: JobExtraService = new JobExtraService(),
        private readonly additionalDetailsService: AdditionalDetailsService = new AdditionalDetailsService()
    ){}

    @Get('/embedded/iframe')
    @UseAuthentication([AuthenticationType.APIKEY])
    async getBookingFormIframe(
        @Res() response: Response,
    ){
        response.send(`<iframe 
            src="http://${process.env.MIDORI_DOMAIN}/api/v1/widget/cleaning"
            style="width: 100%; height: 100%; border: none;"
            title="Midori Book Cleaning Job">
        </iframe>`)
    }

    @Get('/cleaning/')
    @UseAuthentication([AuthenticationType.PARTNER])
    async getCleaningForm(
        @Res() response: Response,
    ) {
        response.render('cleaning/index');
    }

    @Get('/cleaning/form')
    @UseAuthentication([AuthenticationType.PARTNER])
    async getCleaningFormOptions(
        @QueryParams() params: BookingFormQueryParams,
        @CurrentUser() user: PartnerFormUser,
        @Res() response: Response,
    ){
        const serviceDetails = await this.servicePropertySetupService.findAllServicePropertySetupForCompanyWithoutLimit( user.companyId, params.type, true);
        const typeOfServices = await this.typeOfServiceService.findAllTypeOfServicesForCompanyWithoutLimit(user.companyId, params.type, true);
        const serviceAreas = await this.serviceAreaService.findAllServiceAreasByCompanyIdWithoutLimit(user.companyId, params.type, true);
        const serviceFrequencies = await this.serviceFrequencyService.findAllServiceFrequenciesForCompanyByJobType({companyId: user.companyId, type:params.type, isActive:true});
        const jobExtras = await this.jobExtraService.findJobExtrasForCompanyWithoutLimit(user.companyId, params.type, true);
        const additionalDetails = await this.additionalDetailsService.findAllAdditionalDetailsForCompanyNoLimit(user.companyId,params.type, true);
        response.render('cleaning/forms/index', {
            title: 'Midori Book Cleaning Job',
            type: params.type,
            serviceDetails,
            typeOfServices,
            serviceAreas,
            serviceFrequencies,
            jobExtras,
            additionalDetails
        });
    }
}