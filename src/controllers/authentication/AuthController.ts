import { Response } from "express";
import { AuthService } from "../../services";
import { BaseError } from "../../errors";
import { Logging } from "../../util";
import { HTTP_ERROR_TYPES } from "../../constants";
import { Body, CookieParam, HttpCode, JsonController, Post, Res} from 'routing-controllers';
import { ResponseSchema } from "routing-controllers-openapi";
import { UserLoginDto, UserAuthResponseDto, UserTokenDto } from "../../dto/v1";

@JsonController('/v1/auth')
export default class AuthController {

    constructor(
        private readonly logger: Logging = new Logging(),
        private readonly authService: AuthService = new AuthService(logger)
    ){}
    
    @Post('/login')
    @HttpCode(200)
    @ResponseSchema(UserAuthResponseDto, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'User login successfully'
    })
    async signInToAccount(@Res() res: Response, @Body() body: UserLoginDto): Promise<UserAuthResponseDto> {

        const result: UserTokenDto =  await this.authService.loginUser(body)
            
        this.logger.info(`[Success] returning following result: ${JSON.stringify(result)}`)

        this.addCookieToResponse(result.refreshToken, res);

        return this.createResponseBody(result.accessToken);
    };

    @Post('/refresh')
    @HttpCode(201)
    @ResponseSchema(UserAuthResponseDto, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Refresh token generated successfully'
    })
    async refreshToken (@CookieParam('refreshToken') refreshToken: string, @Res() res: Response) {
        if(!refreshToken){
            throw new BaseError(HTTP_ERROR_TYPES.UNKNOWN_AUTHENTICATION_ERROR, 'Currently unable to login. Try again later.');
        }
        
        const result = await this.authService.validateRefreshToken(refreshToken);

        this.logger.info(`[Success] returning following result: ${JSON.stringify(result)}`)

        this.addCookieToResponse(result.refreshToken, res);
        
        return this.createResponseBody(result.accessToken);
    }

    private createResponseBody(accessToken): UserAuthResponseDto {
        return {
            message: "Logged in successfully",
            data: {
                "token": accessToken
            }
        };
    }

    private addCookieToResponse(refreshToken: string, res: Response){
        res.cookie('refreshToken', refreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        });
    }
}