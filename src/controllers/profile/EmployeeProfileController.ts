import { Body, CurrentUser, Delete, Get, HttpCode, JsonController, Param, Patch, Post, UseBefore } from "routing-controllers";
import { EmployeeProfileService } from "../../services";
import { Logging } from "../../util";
import { AuthenticationMiddleware } from "../../middlewares";
import { UserRequestObj } from "../../types";
import { EmployeeProfileResponseDto, EmployeeProfileDto } from "../../dto/v1";
import { ResponseSchema } from "routing-controllers-openapi";

@UseBefore(AuthenticationMiddleware)
@JsonController('/v1/profiles/employees')
export default class EmployeeProfileController {

    constructor(
        private readonly logger: Logging = new Logging(),
        private readonly employeeProfileService: EmployeeProfileService = new EmployeeProfileService(logger)
    ){}

    /** Can be authorized for admin, manager and specific employee */
    @Get('/:profileId/employee')
    @HttpCode(200)
    @ResponseSchema(EmployeeProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Employee users retrieved by profile id successfully'
    })
    async getEmployeeProfileByProfileId (@Param('profileId') profileId: number): Promise<EmployeeProfileResponseDto> {
        const response: EmployeeProfileDto = await this.employeeProfileService.retrieveEmployeeProfileByProfileId(profileId);
        return this.createResponseBody("Profile retrieved successfully", response);
    }

    /** Can only authorized for admin and managers */
    @Get('/')
    @HttpCode(201)
    @ResponseSchema(EmployeeProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Employee users retrieved for company successfully'
    })
    async getEmployeeProfilesByCompanyId (@CurrentUser() user: UserRequestObj): Promise<EmployeeProfileResponseDto> {
        const response: EmployeeProfileDto[] = await this.employeeProfileService.retrieveEmployeeProfilesByCompanyId(user);
        return this.createResponseBody("Profiles retrieved successfully",response);
    }

    /** Can only be authorized for admin or manager */
    @Post('/')
    @HttpCode(201)
    @ResponseSchema(EmployeeProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Employee users created successfully'
    })    
    async createEmployeeProfileByCompanyId (@CurrentUser() user: UserRequestObj, @Body() body: EmployeeProfileDto): Promise<EmployeeProfileResponseDto> {
        const response: EmployeeProfileDto = await this.employeeProfileService.createEmployeeProfileByCompanyId(user, body)
        return this.createResponseBody("Profile Created Successfully", response);
    }

    /** Can be authorized for admin, manager and specific employee */
    @Patch('/:profileId/employee')
    @HttpCode(201)
    @ResponseSchema(EmployeeProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Employee users updated successfully'
    })
    async updateEmployeeProfileByCompanyId (@CurrentUser() user: UserRequestObj, @Body() body: EmployeeProfileDto): Promise<EmployeeProfileResponseDto> {
        const response: EmployeeProfileDto | void = await this.employeeProfileService.updateEmployeeProfileByCompanyId(user, body)
        return this.createResponseBody("Profile updated successfully", response);
    }

    @Delete('/:profileId/employee')
    @HttpCode(204)
    @ResponseSchema(EmployeeProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 204,
        description: 'Employee users updated successfully'
    })
    async deleteEmployeeProfileByCompanyId (@Param('profileId') profileId: number): Promise<EmployeeProfileResponseDto> {
        await this.employeeProfileService.deleteEmployeeProfileByProfileId(profileId)
        return this.createResponseBody("Delete profile successfully");
    }

    private createResponseBody(message: string, response?: EmployeeProfileDto| EmployeeProfileDto[] | void): EmployeeProfileResponseDto {
        return {
            message,
            data: response ? response : null
        };
    }

}