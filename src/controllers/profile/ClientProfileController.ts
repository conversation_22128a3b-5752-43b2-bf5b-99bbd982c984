import { Body, CurrentUser, Delete, Get, HttpCode, JsonController, Param, Patch, Post, UseBefore } from "routing-controllers";
import { ClientProfileService} from "../../services";
import { Logging } from "../../util";
import { AuthenticationMiddleware } from "../../middlewares";
import { ClientProfileResponseDto , ClientProfileDto } from "../../dto/v1";
import { ResponseSchema } from "routing-controllers-openapi";
import { UserRequestObj } from "../../types";

@UseBefore(AuthenticationMiddleware)
@JsonController('/v1/profiles/clients')
export default class ClientProfileController {

    constructor(
        private readonly logger: Logging = new Logging(),
        private readonly clientProfileService: ClientProfileService = new ClientProfileService(logger)
    ){}

    @Get('/:profileId/client')
    @HttpCode(200)
    @ResponseSchema(ClientProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Client users retrieved by profile id successfully'
    })
    async getClientProfileByProfileId (@Param('profileId') profileId: number): Promise<ClientProfileResponseDto> {
        const response: ClientProfileDto = await this.clientProfileService.retrieveClientProfileByProfileId(profileId)
        return this.createResponseBody("Profile retrieved successfully", response);
    }

    /** Can only authorized for admin and managers */
    @Get('/')
    @HttpCode(200)
    @ResponseSchema(ClientProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Employee users retrieved for company successfully'
    })
    async getClientProfilesByCompanyId (@CurrentUser() user: UserRequestObj): Promise<ClientProfileResponseDto> {
        const response:ClientProfileDto[] = await this.clientProfileService.retrieveClientProfilesByCompanyId(user)
        return this.createResponseBody("Profile retrieved successfully", response);

    }

    @Post('/')
    @HttpCode(201)
    @ResponseSchema(ClientProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Client users created successfully'
    })   
    async createClientProfileByCompanyId(@CurrentUser() user: UserRequestObj, @Body() body: ClientProfileDto): Promise<ClientProfileResponseDto> {
        const clientProfileDto: ClientProfileDto = await this.clientProfileService.createClientProfileByCompanyId(user, body);
        return this.createResponseBody("Client profile created successfully", clientProfileDto);
    }

    @Patch('/:clientId/client')
    @HttpCode(201)
    @ResponseSchema(ClientProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Client users created successfully'
    })  
    async updateClientProfileByCompanyId(@CurrentUser() user: UserRequestObj, @Body() body: ClientProfileDto): Promise<ClientProfileResponseDto> {
        const response: ClientProfileDto | void = await this.clientProfileService.updateClientProfileByCompanyId(user, body)
        return this.createResponseBody("Client profile created successfully", response);

    }

    @Delete('/:clientId/client')
    @HttpCode(204)
    @ResponseSchema(ClientProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 204,
        description: 'Client users updated successfully'
    })
    async deleteEmployeeProfileByCompanyId (@Param('clientId') profileId: number): Promise<ClientProfileResponseDto> {
        const response = await this.clientProfileService.deleteClientProfileByProfileId(profileId)
        return this.createResponseBody("Delete profile successfully");
    }

    private createResponseBody(message: string, response?: ClientProfileDto | ClientProfileDto[] | void): ClientProfileResponseDto {
        return {
            message,
            data: response ? response : null
        };
    }
}