import { Response } from "express";
import { Body, CurrentUser, Delete, Get, HttpCode, JsonController, Patch, Post, Res, UseBefore } from "routing-controllers";
import { AdminProfileService} from "../../services";
import { Logging } from "../../util";
import { AuthenticationMiddleware } from "../../middlewares";
import { UserRequestObj } from "../../types";
import { ResponseSchema, OpenAPI } from "routing-controllers-openapi";
import { AdminProfileDto, AdminProfileResponseDto} from "../../dto/v1";

@JsonController('/v1/profiles/admins')
export default class AdminProfileController {

    constructor(
        private readonly logger: Logging = new Logging(),
        private readonly adminProfileService: AdminProfileService = new AdminProfileService(logger),
    ){}

    @UseBefore(AuthenticationMiddleware)
    @Get('/')
    @HttpCode(200)
    @ResponseSchema(AdminProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Admin users retrieved successfully'
    })
    async getAdminProfileByUserId (@CurrentUser() user: UserRequestObj): Promise<AdminProfileResponseDto> {
        const response = await this.adminProfileService.retrieveAdminProfileByUserId(user.userId)
        return this.createResponseBody(response);
    }

    @Post('/')
    @HttpCode(201)
    @ResponseSchema(AdminProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Admin users retrieved successfully'
    })
    async createAdminProfile (@Res() response: Response, @CurrentUser() user: UserRequestObj, @Body() body: AdminProfileDto): Promise<AdminProfileResponseDto>{
        const {adminProfileDto, accessToken, refreshToken} = await this.adminProfileService.createAdminProfileByProfileId(user, body);
        this.addCookieToResponse(refreshToken, response);
        return this.createResponseBody(adminProfileDto, accessToken);
    }

    @UseBefore(AuthenticationMiddleware)
    @Patch('/')
    @HttpCode(201)
    @ResponseSchema(AdminProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Admin users retrieved successfully'
    })
    async updateAdminProfile(@CurrentUser() user: UserRequestObj, @Body() body: AdminProfileDto ): Promise<AdminProfileResponseDto> {
        const response = await this.adminProfileService.updateAdminProfileByProfileId(user, body)
        return this.createResponseBody(response);
    }

    @UseBefore(AuthenticationMiddleware)
    @Delete('/')
    @HttpCode(201)
    @ResponseSchema(AdminProfileResponseDto, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Admin users retrieved successfully'
    })
    async deleteAdminProfile(@CurrentUser() user: UserRequestObj, @Body() body: AdminProfileDto ): Promise<AdminProfileResponseDto> {
        const response = await this.adminProfileService.deleteAdminProfileByProfileId(user);
        return this.createResponseBody(response);
    }

    private createResponseBody(dto: AdminProfileDto, accessToken?:string): AdminProfileResponseDto {
        return {
            message: "Profile retrieved successfully",
            accessToken,
            data: dto
        };
    }

    private addCookieToResponse(refreshToken: string, response: Response){
        response.cookie('refreshToken', refreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        });
    }

}