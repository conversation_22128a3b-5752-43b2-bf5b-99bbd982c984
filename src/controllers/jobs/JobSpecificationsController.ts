import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>er, CurrentU<PERSON>, Get, QueryParams } from "routing-controllers";
import { AuthenticationMiddleware } from "../../middlewares";
import { ResponseSchema } from "routing-controllers-openapi";
import { PaginatedResponseDto, ResponseJobSpecificationDto } from "../../dto/v1";
import { UserRequestObj } from "../../types";
import { AdditionalDetailsService, JobExtraService, ServiceAreaService, ServiceFrequencyService, ServicePropertySetupService, TypeOfServiceService } from "../../services";
import { JobSpecificationsQueryParams } from "./JobSpecificationsQueryParams";

@UseBefore(AuthenticationMiddleware)
@JsonController('/v1/jobs')
export class JobSpecificationsController{

    constructor(
        private readonly serviceAreaService: ServiceAreaService = new ServiceAreaService(),
        private readonly servicePropertySetupService: ServicePropertySetupService = new ServicePropertySetupService(),
        private readonly typeOfServiceService: TypeOfServiceService = new TypeOfServiceService(),
        private readonly serviceFrequencyService: ServiceFrequencyService = new ServiceFrequencyService(),
        private readonly jobExtraService: JobExtraService = new JobExtraService(),
        private readonly additionalDetailsService: AdditionalDetailsService = new AdditionalDetailsService()
    ){}

    @Get('/job-specifications')
    @ResponseSchema(PaginatedResponseDto<ResponseJobSpecificationDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Get all jobs for client using client id'
    })
    async getJobSpecificationsForCompany(
        @CurrentUser() user: UserRequestObj,
        @QueryParams() params: JobSpecificationsQueryParams,
    ): Promise<PaginatedResponseDto<ResponseJobSpecificationDto>> {
        const serviceDetails = await this.servicePropertySetupService.findAllServicePropertySetupForCompanyWithoutLimit(user.companyId, params.type, true);
        const typeOfServices = await this.typeOfServiceService.findAllTypeOfServicesForCompanyWithoutLimit(user.companyId, params.type, true);
        const serviceAreas = await this.serviceAreaService.findAllServiceAreasByCompanyIdWithoutLimit(user.companyId, params.type, true);
        const serviceFrequencies = await this.serviceFrequencyService.findAllServiceFrequenciesForCompanyByJobType({companyId: user.companyId, type:params.type, isActive:true});
        const jobExtras = await this.jobExtraService.findJobExtrasForCompanyWithoutLimit(user.companyId, params.type, true);
        const additionalDetails = await this.additionalDetailsService.findAllAdditionalDetailsForCompanyNoLimit(user.companyId,params.type, true);
        return new PaginatedResponseDto<ResponseJobSpecificationDto>(
            `Successfully retrieved specifications for company`, 
            Array.of({
                serviceDetails, 
                typeOfServices, 
                serviceAreas,
                serviceFrequencies,
                jobExtras,
                additionalDetails
            }));
    }

}