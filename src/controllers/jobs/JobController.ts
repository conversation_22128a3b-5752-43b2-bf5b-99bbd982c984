import { JobService } from "../../services";
import { Body, CurrentUser, Delete, Get, JsonController, Param, Patch, Post, QueryParams, UseBefore } from "routing-controllers";
import { ResponseSchema } from "routing-controllers-openapi";
import { AuthenticationMiddleware } from "../../middlewares";
import { UserRequestObj } from "../../types";
import { CreateResidentialCleaningJobDto, PaginatedResponseDto, ResponseScheduledJobDto, UpdateResidentialCleaningJobDto } from "../../dto/v1";
import { JobQueryParams } from "./JobQueryParams";

@UseBefore(AuthenticationMiddleware)
@JsonController('/v1/jobs')
export class JobController {

    constructor(
        private jobService: JobService,
    ){}

    @Get('/')
    @ResponseSchema(PaginatedResponseDto<ResponseScheduledJobDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Get all jobs for company'
    })
    public async getAllResidentialCleaningJobsByCompanyId(
        @CurrentUser() user: UserRequestObj,
        @QueryParams() params: JobQueryParams,
        ): Promise<PaginatedResponseDto<ResponseScheduledJobDto>> {

        if(params.view === 'calendar'){
            const result = await this.jobService.findScheduledJobsByDateRange(user.companyId, params.startDate, params.endDate);
            return new PaginatedResponseDto<ResponseScheduledJobDto>('Successfully retrieved jobs', result);
        }else if (params.view === 'table') {
            const {result, total} = await this.jobService.findScheduledJobsForCompanyByPage(user.companyId, params.offset, params.limit, params.startDate, params.endDate);
            return new PaginatedResponseDto<ResponseScheduledJobDto>('Successfully retrieved jobs', result, params.offset, params.limit, total);
        }
    }

    @Get('/:jobId/scheduled/:id')
    @ResponseSchema(PaginatedResponseDto<ResponseScheduledJobDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Get scheduled job per id'
    })
    public async getResidentialCleaningJobByJobId(
        @CurrentUser() user: UserRequestObj,
        @Param('id') id: string,
    ): Promise<PaginatedResponseDto<ResponseScheduledJobDto>> {
        const result = await this.jobService.findScheduledJobForCompanyById(id, user.companyId)
        return new PaginatedResponseDto<ResponseScheduledJobDto>(`Successfully retrieved scheduled job for id:${id}`, result);
    }

    @Get('/clients/:clientId')
    @ResponseSchema(PaginatedResponseDto<ResponseScheduledJobDto>, {
        contentType: 'application/json',
        statusCode: 200,
        description: 'Get all jobs for client using client id'
    })
    public async getResidentialCleaningJobsByClientId (
        @Param('clientId') clientId: number,
        @CurrentUser() user: UserRequestObj
    ): Promise<PaginatedResponseDto<ResponseScheduledJobDto>> {
        const result = await this.jobService.findScheduledJobForCompanyByClientId(clientId, user.companyId);
        return new PaginatedResponseDto<ResponseScheduledJobDto>(`Successfully retrieved jobs for clientId:${clientId}`, result);
    }

    @Post('/')
    @ResponseSchema(PaginatedResponseDto<ResponseScheduledJobDto>, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Create a new job for client per company'
    })
    public async createResidentialCleaningJobByCompanyId (
        @CurrentUser() user: UserRequestObj,
        @Body() body: CreateResidentialCleaningJobDto
        ): Promise<PaginatedResponseDto<ResponseScheduledJobDto>> {
        const result = await this.jobService.createJobByCompanyId(body, user.companyId)
        return new PaginatedResponseDto<ResponseScheduledJobDto>(`Successfully created job for clientId:${result[0].client.profileId}`, result);
    }


    @Patch('/:jobId/scheduled/:id')
    @ResponseSchema(PaginatedResponseDto<ResponseScheduledJobDto>, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Update job for client per company'
    })
    public async updateResidentialCleaningJobByJobId (
        @Param('id') id: string,
        @CurrentUser() user: UserRequestObj,
        @Body() body: UpdateResidentialCleaningJobDto
    ) {
        const result = await this.jobService.updateScheduledJobById(body, id, user.companyId)
        return new PaginatedResponseDto<ResponseScheduledJobDto>(`Successfully updated job for clientId:${result[0].client.profileId}`, result);
    }

    @Post('/:jobId/scheduled/:id/cancel')
    @ResponseSchema(PaginatedResponseDto<ResponseScheduledJobDto>, {
        contentType: 'application/json',
        statusCode: 201,
        description: 'Cancel job per job id'
    })
    public async cancelScheduledJobByJobId (
        @Param('id') id: string,
        @CurrentUser() user: UserRequestObj,
        ):Promise<PaginatedResponseDto<ResponseScheduledJobDto>> {
            await this.jobService.cancelScheduledJobByJobId(id, user.companyId);
            return new PaginatedResponseDto<ResponseScheduledJobDto>(`Successfully deleted job ${id}`);
    }

    @Delete('/:jobId')
    @ResponseSchema(PaginatedResponseDto<ResponseScheduledJobDto>, {
        contentType: 'application/json',
        statusCode: 204,
        description: 'Delete job per job id'
    })
    public async deleteJobPermanentlyByJobId (
        @Param('jobInfoId') jobInfoId: string,
        @CurrentUser() user: UserRequestObj,
        ):Promise<PaginatedResponseDto<ResponseScheduledJobDto>> {
            await this.jobService.deleteJobPermanentlyByJobId(jobInfoId, user.companyId);
            return new PaginatedResponseDto<ResponseScheduledJobDto>(`Successfully deleted job ${jobInfoId}`);
    }
}
