import { IsS<PERSON>, IsNotEmpty, ValidateIf, IsDateString, IsEnum, IsOptional } from "class-validator";
import { PaginatedQueryParams } from "../PaginatedQueryParams";

export class JobQueryParams extends PaginatedQueryParams {
    @IsString()
    @IsEnum(['table', 'calendar'], { message: 'view must be either "table" or "calendar"' })
    @IsNotEmpty()
    view: string

    @IsOptional()
    @ValidateIf((o) => o.view === 'calendar')
    @IsNotEmpty({ message: 'startDate is required when view is "table"' })
    @IsDateString({}, { message: 'startDate must be a valid ISO 8601 date string' })
    startDate?: string;

    @IsOptional()
    @ValidateIf((o) => o.view === 'calendar')
    @IsNotEmpty({ message: 'startDate is required when view is "table"' })
    @IsDateString({}, { message: 'endDate must be a valid ISO 8601 date string' })
    endDate?: string;

    @ValidateIf((o) => o.view === 'table')
    limit?: number;

    @ValidateIf((o) => o.view === 'table')
    offset?: number
}