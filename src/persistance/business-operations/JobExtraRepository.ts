import { JobType } from '../../types/types';
import { AppDatabaseClient } from "../../configs";
import { JobExtraEntity } from "../../entity/v1";
import { Logging } from "../../util";
import { In } from 'typeorm';

export class JobExtraRepository {

    constructor(
        private readonly jobExtraRepository = AppDatabaseClient.getRepository(JobExtraEntity)
    ){}

    async findResidentialCleaningExtrasPerCompanyId (companyId: number, offset: number, limit: number): Promise<[JobExtraEntity[], number]> {
        return await this.jobExtraRepository.findAndCount({
            where: {related_company_id: companyId},
            order: {
                name: 'ASC'
            },
            skip: offset,
            take: limit
        })
    }
    async findJobExtrasForCompanyWithoutLimit(companyId: number, type?: JobType, active?: boolean): Promise<JobExtraEntity[]> {
        return await this.jobExtraRepository.find({
            where: {related_company_id: companyId, type_of_cleaning: type, is_active: active},
            order: {
                name: 'ASC'
            },
        })
    }

    async findResidentialCleaningExtraPerExtraId(id: string, companyId: number): Promise<JobExtraEntity> {
        return await this.jobExtraRepository.findOneByOrFail({id: id, related_company_id: companyId});
    }

    async findJobExtrasPriceById(jobExtraIds: string[], companyId: number): Promise<JobExtraEntity[]> {
        return await this.jobExtraRepository.find({
            select: ['id','price','currency'],
            where: {id: In(jobExtraIds), related_company_id: companyId}
        });
    }

    async createResidentialCleaningExtrasPerCompanyId (entity: JobExtraEntity): Promise<JobExtraEntity> {
        return await this.jobExtraRepository.save(entity);
    }

    async updateResidentialCleaningExtrasPerCompanyId(entity: Partial<JobExtraEntity>): Promise<JobExtraEntity> {
        await this.jobExtraRepository.save(entity);
        return await this.findResidentialCleaningExtraPerExtraId(entity.id, entity.related_company_id);
    }

    async deleteResidentialCleaningExtrasPerCompanyId(id: string, companyId: number) {
        return await this.jobExtraRepository.delete({id: id, related_company_id: companyId});
    }
}