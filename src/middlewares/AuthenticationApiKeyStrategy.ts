import { Request, Response } from "express";
import { AuthenticationStrategyInterface } from "./AuthenticationStrategyInterface";
import { HTTP_ERROR_TYPES } from "../constants";
import { BaseError } from "../errors";
import { CompanyAllowedDomainService } from "../services";
import { generatePartnerToken } from "../util/";
import { PartnerFormUser } from "../types";

export class AuthenticateApiKeyStrategy implements AuthenticationStrategyInterface{
    use(request: Request, response: Response, next: (err?: any) => any): void {
        const apiKey = request.headers['x-api-key'] as string
        const domain = request.headers['x-partner-domain'] as string

        if(!apiKey || !domain){
            next(new BaseError(HTTP_ERROR_TYPES.UNKNOWN_AUTHENTICATION_ERROR, `ApiKey and Domain headers required`))
        }

        new CompanyAllowedDomainService().findPartnerIdByKeyAndDomain(apiKey, domain)
        .then(companyId => {
            const token = generatePartnerToken(companyId); 

            response.cookie('partnerAccessToken', token, {
                httpOnly: true,
                sameSite: 'lax',
                secure: true,
                maxAge: 86400,
                path: '/'
            })
            
            next()
        }).catch(error => {
            next(error)
        })
    }
}