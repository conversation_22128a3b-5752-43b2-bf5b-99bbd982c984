import { Request, Response, NextFunction } from "express";
import { Permissions } from "../models/v1";
import { AuthPersistance } from "../db";
import { UserAuthEntity } from "../entity/v1";
import { Logging } from "../util";

export const authorization = (roles: string[] , permission: string) => {
    return async (req: Request, res: Response, next: NextFunction): Promise<any> => {
      
        const authPersistance: AuthPersistance = new AuthPersistance(new Logging());
        const user: UserAuthEntity = await authPersistance.findUserCredentialsById(req.body.decoded_token.user_id);

        if (!user || !roles.includes(user.role)) {
            return res.status(401).json({ error: 'Access denied.' });
        }

        // const userPermissions = new Permissions().getPermissionsByRoleName(user.role);
    
        // if (userPermissions.includes(permission)) {
        return next();
        // } else {
        //   return res.status(403).json({ error: 'Access denied' });
        // }
    }
}
 