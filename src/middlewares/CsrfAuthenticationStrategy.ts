import { Request, Response } from "express";
import { AuthenticationStrategyInterface } from "./AuthenticationStrategyInterface";
import { generateCsrfToken, validateCsrfToken } from "../configs";

export class CsrfAuthenticationStrategy implements AuthenticationStrategyInterface{
    use(request: Request, response: Response, next: (err?: any) => any): void {
        try{
            const {refresh} = validateCsrfToken(request);

            if(refresh){
                generateCsrfToken(response);
            }
        }catch(error){
            next(error);
        }
    }

}