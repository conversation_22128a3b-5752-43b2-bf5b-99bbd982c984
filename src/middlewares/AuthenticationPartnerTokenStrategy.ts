import passport from "passport";
import jwt from 'jsonwebtoken';
import { Request, Response } from 'express';
import { AuthenticationStrategyInterface } from './AuthenticationStrategyInterface';
import { PartnerFormUser, PartnerTokenType } from "../types";
import { HTTP_ERROR_TYPES } from "../constants";
import { AuthenticationError } from "../errors";

export class AuthenticationPartnerTokenStrategy implements AuthenticationStrategyInterface{
    use(request: Request, _: Response, next: (err?: any) => any): void {
        const token = request.cookies['partnerAccessToken'];

        if(!token){
            next(new AuthenticationError({
                name: HTTP_ERROR_TYPES.UNKNOWN_AUTHENTICATION_ERROR,
                message: "<PERSON><PERSON> is missing." 
            }));
        }
        const decodedPartnerToken = this.validatePartnerToken(token);

        if(!decodedPartnerToken) {
            next(new AuthenticationError({
                name: HTTP_ERROR_TYPES.UNKNOWN_AUTHENTICATION_ERROR,
                message: "Token is malformed." 
            }));
        }
        request.user = decodedPartnerToken as PartnerFormUser;
        next()
    }

    private  validatePartnerToken(token: string ){
        if(!process.env.JWT_SECRET){
            throw new Error('JWT_KEY must be defined')
        }
    
        return jwt.verify(token, process.env.JWT_SECRET)
    }

}