// import { Request, Response, NextFunction } from "express";
// import { BaseDTO, Profile } from "../models";
// import { ProfilePersistance } from "../db";
// import { ProfileUserTypes } from "../constants";
// import { plainToInstance } from "class-transformer";
// import { validate } from "class-validator";
// import { sanitize } from "class-sanitizer";

// const validateUserIsFromCompany = async (req: Request, res: Response, next: NextFunction) => {
//     const user_profile: Profile.UserProfileModel = await ProfilePersistance.retrieveProfileByUserId(req.body.decoded_token.user_id);
//     if(user_profile.profile_id){
//         const company_profile: Profile.CompanyProfileModel = await ProfilePersistance.retrieveCompanyById(user_profile.profile_id)

//         if(Number(req.params.company_id) !== company_profile.company_id){
//             res.status(401).json({error:"User from the following company is not authorized."})
//         }

//     }
//     next();
// }

// const validateUserIsEmployee = async (req: Request, res: Response, next: NextFunction) => {
//     const user_profile: Profile.UserProfileModel = await ProfilePersistance.retrieveProfileByUserId(req.body.decoded_token.user_id);
//     if(user_profile.profile_type  !== ProfileUserTypes.EMPLOYEE){
//         res.status(401).json({error:"You are not an employee."})
//     }
//     next();
// }

// const validateUserIsClient = async (req: Request, res: Response, next: NextFunction) => {
//     const user_profile: Profile.UserProfileModel = await ProfilePersistance.retrieveProfileByUserId(req.body.decoded_token.user_id);
//     if(user_profile.profile_type  !== ProfileUserTypes.CLIENT){
//         res.status(401).json({error:"You are not an client."})
//     }
//     next();
// }

// const validateReqBody = (validationSchema: any) => async (req: Request, res: Response, next: NextFunction) => {
//     const dto: any = plainToInstance(validationSchema, req.body);
//     validate(dto, { skipMissingProperties: true }).then(errors => {
//         if (errors.length > 0) {
//             console.error(errors);
//             let errorTexts = Array();
//             for (const errorItem of errors) {
//                 errorTexts = errorTexts.concat(errorItem.constraints);
//             }
//             return res.status(400).send(errorTexts);
//         } else {
//             sanitize(dto)
//             req.body = dto;
//             next();
//         }
//     });

// }


// export {validateUserIsFromCompany, validateUserIsEmployee, validateUserIsClient, validateReqBody}