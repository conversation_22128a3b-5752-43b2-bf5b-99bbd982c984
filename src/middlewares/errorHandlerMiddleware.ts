import { Request, Response, NextFunction } from "express";
import { AuthenticationError, BaseError } from "../errors";
import { HTTP_ERROR_TYPES } from "../constants";
import { ExpressErrorMiddlewareInterface, Middleware } from "routing-controllers";
import { ValidationError } from "class-validator";
import { TypeORMError } from "typeorm";
import { Logging } from "../util";

@Middleware({ type: 'after' })
export class ErrorHandlerMiddleware implements ExpressErrorMiddlewareInterface {
    error(error: any, _: Request, res: Response, next: NextFunction) {
        const logger = new Logging();
        logger.error(error);
        if(error instanceof AuthenticationError){
            return res.status(401).json(this.errorHandler(error))
        }else if(error.errors && (error.errors[0] instanceof ValidationError)){
            return res.status(error.httpCode).json(this.errorHandler(error));
        }else if(error instanceof TypeORMError){
            return res.status(500).json(this.errorHandler(new BaseError(HTTP_ERROR_TYPES.UNKNOwN_ERROR, 'Something went wrong')))
        }else{
            res.status(404).json(this.errorHandler(error));
        }

        return res.status(500).json(this.errorHandler(new BaseError(HTTP_ERROR_TYPES.UNKNOwN_ERROR, 'Something went wrong')));
    }

    private errorHandler(error: any) {
        return {         
            type: error.name,
            message: error.message,
            errors: error.errors
        }
    }
}