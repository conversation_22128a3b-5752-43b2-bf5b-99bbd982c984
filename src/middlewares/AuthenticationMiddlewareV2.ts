import passport from "passport";
import { Middleware } from "routing-controllers";
import jwt from 'jsonwebtoken';
import { UserRequestObj } from "../types";
import { AuthenticationStrategyInterface } from "./AuthenticationStrategyInterface";

@Middleware({ type: 'before' })
export class AuthenticationMiddleware implements AuthenticationStrategyInterface {
    use(request: any, response: any, next: (err?: any) => any) {
        
        passport.authenticate('jwt', (error:any, payload: any, _:any) => {

            if(error){
                response.status(401).json("Unable to login. Please try again later.");
            }else if(!payload){
                response.status(401).json("Unable to login. Please try again later.");
            }else if(!request.headers.authorization){
                response.status(401).json("Unable to login. Please try again later.");
            }else{
                const token = request.headers.authorization.replace('Bearer ', '');
                const decodedAccessToken = this.verifyJwtToken(token);
                request.user = decodedAccessToken as UserRequestObj;
    
                return next();
            }
        })(request, response, next);
    }

    private verifyJwtToken (token: any) {

        if(!process.env.JWT_SECRET){
            throw new Error('JWT_KEY must be defined')
        }
    
        return jwt.verify(token, process.env.JWT_SECRET);
    }
}