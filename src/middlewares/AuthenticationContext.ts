import { UseBefore } from "routing-controllers";
import { AuthenticationType } from "../types";
import { AuthenticationMiddleware } from "./AuthenticationMiddlewareV2";
import { AuthenticationStrategyInterface } from "./AuthenticationStrategyInterface";
import { Request, Response, NextFunction } from "express";
import { CsrfAuthenticationStrategy } from "./CsrfAuthenticationStrategy";
import { AuthenticateApiKeyStrategy } from './AuthenticationApiKeyStrategy';
import { AuthenticationPartnerTokenStrategy } from "./AuthenticationPartnerTokenStrategy";

export class AuthenticationContext {
    private strategies: Map<AuthenticationType, AuthenticationStrategyInterface>;

    constructor(){
        this.strategies = new Map<AuthenticationType, AuthenticationStrategyInterface>([
            [AuthenticationType.JWT, new AuthenticationMiddleware()],
            [AuthenticationType.CSRF, new CsrfAuthenticationStrategy()],
            [AuthenticationType.PARTNER, new AuthenticationPartnerTokenStrategy()],
            [AuthenticationType.APIKEY, new AuthenticateApiKeyStrategy()]
        ])
    }

    getStrategy(type: AuthenticationType): AuthenticationStrategyInterface {
        return this.strategies.get(type);
    }
}

export function UseAuthentication (
    types: [AuthenticationType.JWT | AuthenticationType.CSRF | AuthenticationType.APIKEY | AuthenticationType.PARTNER] 
){
    const authMiddlewares = types.map((type) => new AuthenticationContext().getStrategy(type));

    return UseBefore((request: Request, response: Response, next: NextFunction) => 
        authMiddlewares.forEach((middleware) => middleware.use(request, response, next))
    )
}