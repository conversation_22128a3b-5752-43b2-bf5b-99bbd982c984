import { EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent } from "typeorm";
import { AdminProfileEntity, UserCompanyAssociationEntity } from "../../entity/v1";
import { UserRoles } from "../../constants";

@EventSubscriber()
export class AdminProfileSubscriber implements EntitySubscriberInterface<AdminProfileEntity> {

  listenTo() {
    return AdminProfileEntity;
  }

  async afterInsert(event: InsertEvent<AdminProfileEntity>) {
    await event.manager.getRepository(UserCompanyAssociationEntity).save({
      userId: event.entity.related_user_id as number,
      companyId: event.entity.company_info.company_id,
      profileId: event.entity.profile_id,
      role: UserRoles.ADMIN
    });
  }

  async afterUpdate(event: UpdateEvent<AdminProfileEntity>) {
    if (event.entity) { 
        await event.manager.getRepository(UserCompanyAssociationEntity).update(
            { id: event.entity.id },
            {
                userId: event.entity.userId as number,
                companyId: event.entity.company_info.company_id,
                profileId: event.entity.profile_id,
                role: UserRoles.ADMIN
            }
        );
    }
  }
}
