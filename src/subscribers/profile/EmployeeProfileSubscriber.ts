import { EventSubscriber, EntitySubscriberInterface, Repository, InsertEvent, DataSource, UpdateEvent } from "typeorm";
import { UserRoles } from "../../constants";
import { EmployeeProfileEntity, UserCompanyAssociationEntity } from "../../entity/v1";

@EventSubscriber()
export class EmployeeProfileSubscriber implements EntitySubscriberInterface<EmployeeProfileEntity> {
  listenTo() {
    return EmployeeProfileEntity;
  }

  async afterInsert(event: InsertEvent<EmployeeProfileEntity>) {
    await event.manager.getRepository(UserCompanyAssociationEntity).save({
      userId: event.entity.related_user_id,
      companyId: event.entity.related_company_id,
      profileId: event.entity.profile_id,
      role: UserRoles.EMPLOYEE
    });
  }

  async afterUpdate(event: UpdateEvent<EmployeeProfileEntity>) {
    if (event.entity) {  // Make sure entity exists
        await event.manager.getRepository(UserCompanyAssociationEntity).update(
            { id: event.entity.id },
            {
                userId: event.entity.userId,
                companyId: event.entity.related_company_id,
                profileId: event.entity.profile_id,
                role: UserRoles.EMPLOYEE
            }
        );
    }
  }
}