import { EventSubscriber, EntitySubscriberInterface, Repository, InsertEvent, DataSource, UpdateEvent } from "typeorm";
import { UserRoles } from "../../constants";
import { ClientProfileEntity, UserCompanyAssociationEntity } from "../../entity/v1";

@EventSubscriber()
export class ClientProfileSubscriber implements EntitySubscriberInterface<ClientProfileEntity> {
  listenTo() {
    return ClientProfileEntity;
  }

  async afterInsert(event: InsertEvent<ClientProfileEntity>) {
    await event.manager.getRepository(UserCompanyAssociationEntity).save({
      userId: event.entity.related_user_id,
      companyId: event.entity.related_company_id,
      profileId: event.entity.profile_id,
      role: UserRoles.CLIENT
    });
  }

  async afterUpdate(event: UpdateEvent<ClientProfileEntity>) {
    if (event.entity) {  // Make sure entity exists
        await event.manager.getRepository(UserCompanyAssociationEntity).update(
            { id: event.entity.id },
            {
                userId: event.entity.userId,
                companyId: event.entity.related_company_id,
                profileId: event.entity.profile_id,
                role: UserRoles.CLIENT
            }
        );
    }
  }
}