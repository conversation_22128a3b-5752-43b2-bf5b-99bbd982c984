export interface UserRequestObj{
    userId: number;
    companyId: number;
    role: string;
}

export interface PartnerFormUser {
    companyId: number
}
export interface PartnerTokenType {
    companyId: number,
    activeSettings: boolean,
}

export enum JobType {
    COMMERCIAL = 'commercial',
    RESIDENTIAL = 'residential'
}

export enum JobStatus {
    PENDING = 'PENDING',
    SCHEDULED = 'SCHEDULED',
    ASSIGNED = 'ASSIGNED',
    CANCELLED = 'CANCELLED',
    COMPLETED = 'COMPLETED'
}

export enum AuthenticationType{
    JWT= "JWT",
    CSRF="CSRF",
    PARTNER = "PARTNER",
    APIKEY = "APIKEY"
}
