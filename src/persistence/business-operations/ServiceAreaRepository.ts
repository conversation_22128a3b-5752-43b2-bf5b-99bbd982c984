import { Entity } from 'typeorm';
import { AppDatabaseClient } from "../../configs";
import { ServiceAreaEntity } from "../../entity/v1";
import { JobType } from '../../types';

export class ServiceAreaRepository {
    constructor(
        private readonly serviceAreaRepository = AppDatabaseClient.getRepository(ServiceAreaEntity)
    ){}

    async findAllServiceAreasByCompanyId(companyId: number,offset: number, limit: number): Promise<[ServiceAreaEntity[], number]> {
        return await this.serviceAreaRepository.findAndCount({
            where:{related_company_id: companyId,is_active: true},
            order: {
                sq_ft_from: 'ASC'
            },
            skip: offset,
            take: limit
        });
    }

    async findAllServiceAreasByCompanyIdWithoutLimit(companyId: number, type?: JobType, active?: boolean): Promise<ServiceAreaEntity[]>{
        return await this.serviceAreaRepository.find(
            {
                where: {related_company_id: companyId, type_of_cleaning: type, is_active: true},
                order: {
                    sq_ft_from: 'ASC'
                }
            }
        );
    }

    async findServiceAreaById(serviceAreaId: string, companyId: number): Promise<ServiceAreaEntity>{
        return await this.serviceAreaRepository.findOneOrFail({
            where: {id: serviceAreaId, related_company_id: companyId},
            order: {
                sq_ft_from: 'ASC'
            }
        });
    }

    async findServiceAreaPriceById(serviceAreaId: string, companyId: number): Promise<ServiceAreaEntity> {
        return await this.serviceAreaRepository.findOneOrFail({
            select: ['id','price','currency'],
            where: {id: serviceAreaId, related_company_id: companyId}
        });
    }

    async createServiceArea(entity: ServiceAreaEntity): Promise<ServiceAreaEntity> {
        return await this.serviceAreaRepository.save(entity);
    }

    async updateServiceAreaById(entity: ServiceAreaEntity): Promise<ServiceAreaEntity> {
        await this.serviceAreaRepository.save(entity);
        return this.findServiceAreaById(entity.id, entity.related_company_id);
    }

    async deleteServiceAreaById(serviceAreaId: string,  companyId: number) {
        return await this.serviceAreaRepository.delete({id: serviceAreaId, related_company_id: companyId});
    }
}