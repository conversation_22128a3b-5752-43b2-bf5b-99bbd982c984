import { CreateAdditionalDetailsChoicesDto, CreateAdditionalDetailsDto, 
    ResponseAdditionalDetailsChoicesDto, ResponseAdditionalDetailsDto, 
    UpdateAdditionalDetailsChoicesDto, UpdateAdditionalDetailsDto } from "../../dto/v1";
import { AdditionalDetailsChoicesEntity, AdditionalDetailsEntity } from "../../entity/v1";
import { IBuilder } from "./IBuilder";

export class AdditionalDetailsBuilder implements IBuilder<AdditionalDetailsEntity, ResponseAdditionalDetailsDto> {
    private entity: AdditionalDetailsEntity;
    private dto: ResponseAdditionalDetailsDto;

    constructor(){
        this.entity = new AdditionalDetailsEntity();
        this.dto = new ResponseAdditionalDetailsDto();
    }

    setCompanyId(companyId: number): AdditionalDetailsBuilder {
        this.entity.related_company_id = companyId;
        return this;
    }

    setAdditionalDetailId(additionalDetailsId: string): AdditionalDetailsBuilder {
        this.entity.id = additionalDetailsId;
        return this;
    }

    fromDto(dto: CreateAdditionalDetailsDto | UpdateAdditionalDetailsDto ): AdditionalDetailsBuilder {
        this.entity.name = dto?.name;
        this.entity.description = dto?.description;
        this.entity.is_active = dto?.isActive;
        this.entity.type_of_cleaning = dto?.typeOfCleaning;
        this.entity.choices = dto.choices?.map((choice) => this.fromChoicesDto(choice));
        return this;
    }

    private fromChoicesDto(dto: CreateAdditionalDetailsChoicesDto | UpdateAdditionalDetailsChoicesDto): AdditionalDetailsChoicesEntity {
        const entity = new AdditionalDetailsChoicesEntity();
        if(dto instanceof UpdateAdditionalDetailsChoicesDto){
            entity.id = dto.detailChoiceId;
        }
        entity.name = dto?.name;
        entity.price = dto?.price;
        entity.currency = dto?.currency;
        return entity;
    }

    fromEntity(entity: AdditionalDetailsEntity): AdditionalDetailsBuilder {
        this.dto.id = entity.id;
        this.dto.name = entity.name;
        this.dto.description = entity?.description;
        this.dto.typeOfCleaning = entity.type_of_cleaning;
        this.dto.isActive = entity.is_active;
        this.dto.choices = entity.choices?.map(choice => this.fromChoicesEntity(choice));
        return this;
    }

    private fromChoicesEntity(entity: AdditionalDetailsChoicesEntity): ResponseAdditionalDetailsChoicesDto {
        const dto = new ResponseAdditionalDetailsChoicesDto();
        dto.id = entity.id;
        dto.name = entity.name;
        dto.price = entity.price;
        dto.currency = entity.currency
        return dto;
    }

    buildEntity(): AdditionalDetailsEntity {
        return this.entity;
    }
    buildDto(): ResponseAdditionalDetailsDto {
        return this.dto;
    }

}