import { JobAssignedPriceEntity } from "../../entity/v1";
import { ResponseJobAssignedPrice } from "../../dto/v1/jobs/JobAssignedPriceDto";
import { IBuilder } from "./IBuilder";

export class PriceBuilder implements IBuilder<JobAssignedPriceEntity, ResponseJobAssignedPrice>{
    private entity: JobAssignedPriceEntity;
    private dto: ResponseJobAssignedPrice;
    
    buildEntity(): JobAssignedPriceEntity {
        return this.entity;
    }
    buildDto() {
        return this.dto;
    }

}