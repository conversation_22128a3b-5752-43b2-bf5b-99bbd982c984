import { ClientProfileDto, CreateResidentialCleaningJobDto, EmployeeProfileDto, ResidentialCleaningAdditionalDetailsDto, 
    ResidentialCleaningJobDetailsDto, ResidentialCleaningJobExtraDto, ResponseScheduledJobDto,
    ResponseServiceAreaDto,ResponseServiceFrequencyDto, ResponseTypeOfServiceDto, UpdateResidentialCleaningJobDto } from "../../dto/v1";
import { ClientProfileEntity, EmployeeProfileEntity, JobAssignedAdditionalDetailsEntity, JobAssignedConfigurationEntity, JobAssignedExtraEntity, JobEntity, ScheduledJobDetailsEntity, ServiceAreaEntity, ServiceFrequencyEntity, TypeOfServiceEntity, } from "../../entity/v1";
import { JobType } from "../../types";
import { IBuilder } from "./IBuilder";

export class JobBuilder implements IBuilder<JobEntity, ResponseScheduledJobDto> {
    private entity: JobEntity;
    private dto: ResponseScheduledJobDto;

    constructor() {
        this.entity = new JobEntity();
        this.dto = new ResponseScheduledJobDto();
        
    }

    setEntityType(type: JobType): JobBuilder {
        this.entity.type = type;
        return this;
    }

    setJobId(id: string): JobBuilder {
        this.entity.id = id;
        return this;
    }

    setEntityCompanyId(companyId: number): JobBuilder {
        this.entity.related_company_id = companyId;
        return this;
    }

    setScheduledDetailsEntity(scheduledJobEntity: ScheduledJobDetailsEntity[]): JobBuilder {
        this.entity.scheduled_jobs = scheduledJobEntity;
        return this;
    }

    setServiceFrequencyEntity(serviceFrequency: ServiceFrequencyEntity): JobBuilder {
        this.entity.service_frequency = serviceFrequency;
        return this;
    }

    setLastJobGeneratedDatetime(lastJobGeneratedDatetime: Date): JobBuilder {
        this.entity.last_job_generated_datetime = lastJobGeneratedDatetime;
        return this;
    }

    setOldJobInfoOptions(currentEntity: JobEntity): JobBuilder{
        this.entity.id = currentEntity.id;
        this.entity.related_service_frequency_id = currentEntity.service_frequency?.id;
        this.entity.related_client_id = currentEntity.client_info?.profile_id;
        return this;
    }

    fromDto(dto: CreateResidentialCleaningJobDto | UpdateResidentialCleaningJobDto ): JobBuilder {
        this.entity.related_service_frequency_id = dto.serviceFrequency ? dto.serviceFrequency : this.entity.related_service_frequency_id;
        this.entity.related_client_id = dto.client?.profileId ? dto.client.profileId : this.entity.related_client_id;
        this.entity.is_active = true;

        return this;
    }

    fromEntity(entity: JobEntity, scheduledJob?: ScheduledJobDetailsEntity): JobBuilder {
        this.dto.id = entity.id;
        this.dto.jobId = scheduledJob?.id;
        this.dto.startTime = scheduledJob?.start_time;
        this.dto.endTime = scheduledJob?.end_time;
        this.dto.status = scheduledJob?.status;
        this.dto.totalPrice = scheduledJob?.total_price;
        this.dto.serviceFrequency = entity.service_frequency ? this.mapEntityServiceFrequency(entity.service_frequency) : undefined;
        this.dto.serviceDetails = scheduledJob?.service_details?.map((details) => this.mapEntityJobSpecifications(details));
        this.dto.serviceArea = scheduledJob?.service_area ? this.mapEntityServiceArea(scheduledJob.service_area) : undefined;
        this.dto.serviceType = scheduledJob?.service_type ? this.mapEntityServiceType(scheduledJob.service_type) : undefined;
        this.dto.extras = scheduledJob?.extras?.map((extra) => this.mapEntityJobExtras(extra));
        this.dto.additionalDetails = scheduledJob?.additionalDetails?.map((additionalDetail) => this.mapEntityAdditionalDetails(additionalDetail));
        this.dto.assignedEmployees = scheduledJob?.assigned_employees?.map((assignedEmployee) => this.mapEntityEmployeeProfile(assignedEmployee.employee_profile));
        this.dto.client = entity.client_info ? this.mapEntityClientProfile(entity.client_info) : undefined;
        return this;
    }

    private mapEntityClientProfile(clientProfile: ClientProfileEntity): ClientProfileDto {
        return {
            profileId: clientProfile.profile_id,
            firstName: clientProfile.first_name,
            lastName: clientProfile.last_name,
            phoneNumber: clientProfile.phone_number,
            email: clientProfile.email,
            addressInfo: clientProfile.street || clientProfile.state || clientProfile.city || clientProfile.postal_code ? {
                street: clientProfile.street,
                city: clientProfile.city,
                state: clientProfile.state,
                postalCode: clientProfile.postal_code,
            } : undefined,
            companyName: clientProfile.company_name
        };
    }

    private mapEntityEmployeeProfile(employeeProfile: EmployeeProfileEntity): EmployeeProfileDto {
        return {
            profileId: employeeProfile.profile_id,
            firstName: employeeProfile.first_name,
            lastName: employeeProfile.last_name,
            phoneNumber: employeeProfile.phone_number,
            email: employeeProfile.email,
            dateOfBirth: employeeProfile.date_of_birth,
            position: employeeProfile.position,
            addressInfo: employeeProfile.street || employeeProfile.state || employeeProfile.city || employeeProfile.postal_code ? {
                street: employeeProfile.street,
                city: employeeProfile.city,
                state: employeeProfile.state,
                postalCode: employeeProfile.postal_code,
            } : undefined,
        }
    }

    private mapEntityServiceFrequency(serviceFreq: ServiceFrequencyEntity): ResponseServiceFrequencyDto {
        return {
            id:serviceFreq.id,
            name: serviceFreq.name,
            frequencyConfig: {
                name: serviceFreq.frequency_name,
                defaultInterval: serviceFreq.frequency_interval
            }
        }
    }

    private mapEntityJobSpecifications(jobAssignedConfiguration: JobAssignedConfigurationEntity): ResidentialCleaningJobDetailsDto {
        return {
            id: jobAssignedConfiguration.id,
            name: jobAssignedConfiguration?.service_property_setup?.name,
            amount: jobAssignedConfiguration.amount
        }
    }

    private mapEntityJobExtras(jobAssignedExtra: JobAssignedExtraEntity): ResidentialCleaningJobExtraDto {
        return {
            id: jobAssignedExtra.id,
            name: jobAssignedExtra?.extra?.name,
            amount: jobAssignedExtra.amount
        }
    }

    private mapEntityAdditionalDetails(jobAssignedAdditionalDetail: JobAssignedAdditionalDetailsEntity): ResidentialCleaningAdditionalDetailsDto {
        return {
            id: jobAssignedAdditionalDetail.additionalDetail.id,
            name: jobAssignedAdditionalDetail.additionalDetail?.name,
            choiceId: jobAssignedAdditionalDetail.choice?.id,
            choiceName: jobAssignedAdditionalDetail.choice?.name,
        }
    }

    private mapEntityServiceType(serviceType: TypeOfServiceEntity): ResponseTypeOfServiceDto {
        return {
            id: serviceType.id,
            name: serviceType.name
        }
    }

    private mapEntityServiceArea(serviceArea: ServiceAreaEntity): ResponseServiceAreaDto {
        return {
            id:serviceArea.id,
            sqFtFrom: serviceArea.sq_ft_from,
            sqFtTo: serviceArea.sq_ft_to
        }
    }

 
    buildEntity(): JobEntity {
        return this.entity;
    }

    buildDto(): ResponseScheduledJobDto {
        return this.dto;
    }

}