import { CreateServicePropertySetupDto, ResponseServicePropertySetupDto, UpdateServicePropertySetupDto } from "../../dto/v1";
import { ServicePropertySetupEntity } from "../../entity/v1";
import { IBuilder } from "./IBuilder";

export class ServicePropertySetupBuilder implements IBuilder<ServicePropertySetupEntity, ResponseServicePropertySetupDto> {
    private entity: ServicePropertySetupEntity;
    private dto: ResponseServicePropertySetupDto;

    constructor(){
        this.entity = new ServicePropertySetupEntity();
        this.dto = new ResponseServicePropertySetupDto();
    }

    setEntityServicePropertyId(servicePropertyId: string): ServicePropertySetupBuilder {
        this.entity.id = servicePropertyId;
        return this;
    }

    setEntityCompanyId(companyId: number):ServicePropertySetupBuilder{
        this.entity.related_company_id = companyId;
        return this
    }

    fromDto(dto: CreateServicePropertySetupDto | UpdateServicePropertySetupDto): ServicePropertySetupBuilder {
        this.entity.name = dto?.name;
        this.entity.description = dto?.description; 
        this.entity.available_amount = dto?.available_amount;
        this.entity.type_of_cleaning = dto?.type_of_cleaning;
        this.entity.price = dto?.price;
        this.entity.currency = dto?.currency;
        this.entity.is_active = dto?.is_active;
        return this;
    }

    fromEntity(entity: ServicePropertySetupEntity): ServicePropertySetupBuilder {
        this.dto.id = entity?.id;
        this.dto.name = entity?.name;
        this.dto.description = entity?.description; 
        this.dto.available_amount = entity?.available_amount;
        this.dto.type_of_cleaning = entity?.type_of_cleaning;
        this.dto.price = entity?.price;
        this.dto.currency = entity?.currency;
        this.dto.is_active = entity?.is_active;
        return this;
    }

    buildEntity(): ServicePropertySetupEntity {
        return this.entity;
    }
    buildDto(): ResponseServicePropertySetupDto {
        return this.dto;
    }
}