import { CreateTypeOfServiceDto, ResponseTypeOfServiceDto, UpdateTypeOfServiceDto } from "../../dto/v1";
import { TypeOfServiceEntity } from "../../entity/v1";
import { IBuilder } from "./IBuilder";

export class TypeOfServiceBuilder implements IBuilder<TypeOfServiceEntity, ResponseTypeOfServiceDto> {
    private entity: TypeOfServiceEntity;
    private dto: ResponseTypeOfServiceDto;

    constructor(){
        this.entity = new TypeOfServiceEntity();
        this.dto = new ResponseTypeOfServiceDto()
    }

    setCompanyId(companyId: number): TypeOfServiceBuilder {
        this.entity.related_company_id = companyId;
        return this;
    }

    setTypeOfServiceId(typeOfServiceId: string): TypeOfServiceBuilder {
        this.entity.id = typeOfServiceId;
        return this;
    }

    fromDto(dto:CreateTypeOfServiceDto | UpdateTypeOfServiceDto): TypeOfServiceBuilder {
        this.entity.name = dto?.name;
        this.entity.description = dto?.description;
        this.entity.price = dto?.price;
        this.entity.is_active = dto?.isActive;
        this.entity.currency = dto?.currency;
        this.entity.type_of_cleaning = dto?.typeOfCleaning;
        return this;
    }

    fromEntity(entity: TypeOfServiceEntity): TypeOfServiceBuilder {
        this.dto.id = entity.id;
        this.dto.name = entity.name;
        this.dto.description = entity.description;
        this.dto.price = entity.price;
        this.dto.currency = entity.currency;
        this.dto.typeOfCleaning = entity.type_of_cleaning;
        this.dto.isActive = entity.is_active;
        return this;
    }

    buildEntity(): TypeOfServiceEntity {
        return this.entity;
    }
    
    buildDto(): ResponseTypeOfServiceDto {
        return this.dto;
    }
}