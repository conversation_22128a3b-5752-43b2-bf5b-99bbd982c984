import { ClientProfileDto, CreateResidentialCleaningJobDto, EmployeeProfileDto, ResidentialCleaningAdditionalDetailsDto, 
    ResidentialCleaningJobDetailsDto, ResidentialCleaningJobExtraDto, 
    ResponseScheduledJobDto, 
    ResponseServiceAreaDto, ResponseServiceFrequencyDto, ResponseTypeOfServiceDto, UpdateResidentialCleaningJobDto } from "../../dto/v1";
import { ClientProfileEntity, EmployeeProfileEntity, JobAssignedAdditionalDetailsEntity, JobAssignedConfigurationEntity, 
    JobAssignedEmployeeEntity, 
    JobAssignedExtraEntity, JobEntity, ScheduledJobDetailsEntity, ServiceAreaEntity, ServiceFrequencyEntity, TypeOfServiceEntity } from "../../entity/v1";
import { IBuilder } from "./IBuilder";

export class ScheduledJobDetailsBuilder implements IBuilder<ScheduledJobDetailsEntity, ResponseScheduledJobDto> {
    private entity: ScheduledJobDetailsEntity;
    private dto: ResponseScheduledJobDto;

    constructor(){
        this.entity = new ScheduledJobDetailsEntity();
        this.dto = new ResponseScheduledJobDto();
    }

    setScheduledJobDetailsID(id: string): ScheduledJobDetailsBuilder{
        this.entity.id = id;
        return this;
    }

    setEntityEmployeeProfiles(employeeProfiles: JobAssignedEmployeeEntity[]): ScheduledJobDetailsBuilder{
        this.entity.assigned_employees = employeeProfiles;
        return this;
    }

    setJobInfoEntity(jobInfoEntity: JobEntity): ScheduledJobDetailsBuilder {
        this.entity.job_info = jobInfoEntity;
        return this;
    }

    setScheduledJobPrice(totalPrice: number): ScheduledJobDetailsBuilder {
        this.entity.total_price = totalPrice;
        return this;
    }
 
    setOldScheduledJobOptions(currentEntity: ScheduledJobDetailsEntity): ScheduledJobDetailsBuilder{
        this.entity.id = currentEntity.id;
        this.entity.related_service_area_id = currentEntity?.service_area?.id;
        this.entity.related_service_type_id = currentEntity?.service_type?.id;
        this.entity.service_details = currentEntity.service_details;
        this.entity.extras = currentEntity.extras;
        this.entity.additionalDetails = currentEntity.additionalDetails;
        return this;
    }

    setEntityCompanyId(companyId: number): ScheduledJobDetailsBuilder {
        this.entity.related_company_id = companyId;
        return this;
    }

    fromDto(dto: CreateResidentialCleaningJobDto | UpdateResidentialCleaningJobDto): ScheduledJobDetailsBuilder{
        this.entity.start_time = dto?.startTime;
        this.entity.end_time = dto?.endTime;
        this.entity.status = dto?.status;
        this.entity.related_service_area_id = dto.serviceArea ? dto.serviceArea : this.entity.related_service_area_id;
        this.entity.related_service_type_id = dto.serviceType ? dto.serviceType : this.entity.related_service_type_id;

        if(dto instanceof UpdateResidentialCleaningJobDto){
            this.entity.service_details?.find((detail) => this.mapEntityDetailsServiceToNewAmount(detail, dto.serviceDetails))
            this.entity.extras?.find((extra) => this.mapEntityJobExtraToNewAmount(extra, dto.extras))
            this.entity.additionalDetails?.find((additionalDetail) => this.mapEntityJobAdditionalDetailsToNewChoice(additionalDetail, dto.additionalDetails))

        }else if(dto instanceof CreateResidentialCleaningJobDto){
            this.entity.service_details = dto.serviceDetails?.map((details) => this.mapDtoServiceDetails(details))
            this.entity.extras = dto.extras?.map((extra) => this.mapDtoJobExtra(extra))
            this.entity.additionalDetails = dto.additionalDetails?.map((additionalDetail) => this.mapDtoAdditionalDetail(additionalDetail))
        }

        return this;
    }

    fromEntity(entity: ScheduledJobDetailsEntity): ScheduledJobDetailsBuilder {
        this.dto.id = entity.id;
        this.dto.jobId = entity.job_info?.id;
        this.dto.startTime = entity.start_time;
        this.dto.endTime = entity.end_time;
        this.dto.status = entity.status;
        this.dto.totalPrice = entity.total_price;
        this.dto.serviceFrequency = entity.job_info?.service_frequency ? this.mapEntityServiceFrequency(entity.job_info.service_frequency) : undefined;
        this.dto.serviceDetails = entity.service_details?.map((details) => this.mapEntityJobSpecifications(details));
        this.dto.serviceArea = entity.service_area ? this.mapEntityServiceArea(entity.service_area) : undefined;
        this.dto.serviceType = entity.service_type ? this.mapEntityServiceType(entity.service_type) : undefined;
        this.dto.extras = entity.extras?.map((extra) => this.mapEntityJobExtras(extra));
        this.dto.additionalDetails = entity.additionalDetails?.map((additionalDetail) => this.mapEntityAdditionalDetails(additionalDetail));
        this.dto.assignedEmployees = entity.assigned_employees?.map((assignedEmployee) => this.mapEntityEmployeeProfile(assignedEmployee.employee_profile));
        this.dto.client = entity.job_info?.client_info ? this.mapEntityClientProfile(entity.job_info.client_info) : undefined;
        return this;
    }

    private mapDtoServiceDetails(servicePropertyDetail: ResidentialCleaningJobDetailsDto): JobAssignedConfigurationEntity {
        const jobAssignedConfig = new JobAssignedConfigurationEntity();
        jobAssignedConfig.related_property_setup_id = servicePropertyDetail.id;
        jobAssignedConfig.amount = servicePropertyDetail.amount;
        jobAssignedConfig.job = this.entity
        return jobAssignedConfig;
    }

    private mapDtoJobExtra(jobExtra: ResidentialCleaningJobExtraDto): JobAssignedExtraEntity {
        const jobAssignedExtra = new JobAssignedExtraEntity();
        jobAssignedExtra.related_extra_id = jobExtra.id;
        jobAssignedExtra.amount = jobExtra.amount;
        jobAssignedExtra.job = this.entity;
        return jobAssignedExtra;
    }

    private mapDtoAdditionalDetail(additionalDetail: ResidentialCleaningAdditionalDetailsDto): JobAssignedAdditionalDetailsEntity {
        const jobAssignedAdditionalDetail = new JobAssignedAdditionalDetailsEntity();
        jobAssignedAdditionalDetail.related_additional_details_id = additionalDetail.id;
        jobAssignedAdditionalDetail.related_additional_details_choice_id = additionalDetail.choiceId;
        jobAssignedAdditionalDetail.job = this.entity;
        return jobAssignedAdditionalDetail;
    }

    private mapEntityClientProfile(clientProfile: ClientProfileEntity): ClientProfileDto {
        return {
            profileId: clientProfile.profile_id,
            firstName: clientProfile.first_name,
            lastName: clientProfile.last_name,
            phoneNumber: clientProfile.phone_number,
            email: clientProfile.email,
            addressInfo: clientProfile.street || clientProfile.state || clientProfile.city || clientProfile.postal_code ? {
                street: clientProfile.street,
                city: clientProfile.city,
                state: clientProfile.state,
                postalCode: clientProfile.postal_code,
            } : undefined,
            companyName: clientProfile.company_name
        };
    }

    private mapEntityServiceFrequency(serviceFreq: ServiceFrequencyEntity): ResponseServiceFrequencyDto {
        return {
            id:serviceFreq.id,
            name: serviceFreq.name,
            frequencyConfig: {
                name: serviceFreq.frequency_name,
                defaultInterval: serviceFreq.frequency_interval
            }
        }
    }

    private mapEntityEmployeeProfile(employeeProfile: EmployeeProfileEntity): EmployeeProfileDto {
        return {
            profileId: employeeProfile.profile_id,
            firstName: employeeProfile.first_name,
            lastName: employeeProfile.last_name,
            phoneNumber: employeeProfile.phone_number,
            email: employeeProfile.email,
            dateOfBirth: employeeProfile.date_of_birth,
            position: employeeProfile.position,
            addressInfo: employeeProfile.street || employeeProfile.state || employeeProfile.city || employeeProfile.postal_code ? {
                street: employeeProfile.street,
                city: employeeProfile.city,
                state: employeeProfile.state,
                postalCode: employeeProfile.postal_code,
            } : undefined,
        }
    }

    private mapEntityJobSpecifications(jobAssignedConfiguration: JobAssignedConfigurationEntity): ResidentialCleaningJobDetailsDto {
        return {
            id: jobAssignedConfiguration.id,
            name: jobAssignedConfiguration?.service_property_setup?.name,
            amount: jobAssignedConfiguration.amount
        }
    }

    private mapEntityJobExtras(jobAssignedExtra: JobAssignedExtraEntity): ResidentialCleaningJobExtraDto {
        return {
            id: jobAssignedExtra.id,
            name: jobAssignedExtra?.extra?.name,
            amount: jobAssignedExtra.amount
        }
    }

    private mapEntityAdditionalDetails(jobAssignedAdditionalDetail: JobAssignedAdditionalDetailsEntity): ResidentialCleaningAdditionalDetailsDto {
        return {
            id: jobAssignedAdditionalDetail.additionalDetail.id,
            name: jobAssignedAdditionalDetail.additionalDetail?.name,
            choiceId: jobAssignedAdditionalDetail.choice?.id,
            choiceName: jobAssignedAdditionalDetail.choice?.name,
        }
    }

    private mapEntityServiceType(serviceType: TypeOfServiceEntity): ResponseTypeOfServiceDto {
        return {
            id: serviceType.id,
            name: serviceType.name
        }
    }

    private mapEntityServiceArea(serviceArea: ServiceAreaEntity): ResponseServiceAreaDto {
        return {
            id:serviceArea.id,
            sqFtFrom: serviceArea.sq_ft_from,
            sqFtTo: serviceArea.sq_ft_to
        }
    }

    private mapEntityDetailsServiceToNewAmount(currentServiceDetail: JobAssignedConfigurationEntity, newServiceDetails: ResidentialCleaningJobDetailsDto[]): void {
        newServiceDetails?.forEach((detail, index) => 
             {
                 if(detail.id === currentServiceDetail.related_property_setup_id){
                     /// If the service details in update already exist, replace amount. 
                     currentServiceDetail.amount = detail.amount;
                     newServiceDetails.splice(index, 1);
                 }else{
                     /// If the service details in update don't exist, add to service details;
                     const updatedServiceDetail = this.mapDtoServiceDetails(detail);
                     this.entity.service_details.push(updatedServiceDetail);
                 }
             }
         );
     }
 
     private mapEntityJobExtraToNewAmount(currentJobExtra: JobAssignedExtraEntity, newJobExtra: ResidentialCleaningJobExtraDto[]): void {
         newJobExtra?.forEach((extra, index) => {
             if(extra.id === currentJobExtra.related_extra_id){
                 currentJobExtra.amount = extra.amount;
                 newJobExtra.splice(index, 1);
             }else{
                 const updatedJobExtra = this.mapDtoJobExtra(extra);
                 this.entity.extras.push(updatedJobExtra);
             }
         })
     }
 
    private mapEntityJobAdditionalDetailsToNewChoice(currentJobAdditionalDetail: JobAssignedAdditionalDetailsEntity, newJobAdditionalDetails: ResidentialCleaningAdditionalDetailsDto[]): void {
        newJobAdditionalDetails?.forEach((additionalDetail, index) => {
            if(additionalDetail.id === currentJobAdditionalDetail.related_additional_details_id){
                currentJobAdditionalDetail.related_additional_details_choice_id = additionalDetail.choiceId;
                currentJobAdditionalDetail.choice = undefined;
                newJobAdditionalDetails.splice(index, 1);
            }else{
                const updatedAdditionalDetail = this.mapDtoAdditionalDetail(additionalDetail);
                this.entity.additionalDetails.push(updatedAdditionalDetail);
            }
        })
    }
    buildEntity(): ScheduledJobDetailsEntity {
        return this.entity;
    }
    buildDto(): ResponseScheduledJobDto {
        return this.dto;
    }
    
}
