import { ServiceFrequencyEnum } from "../../constants/types";
import { ResponseServiceFrequencyDto, CreateServiceFrequencyDto, UpdateServiceFrequencyDto } from "../../dto/v1";
import { ServiceFrequencyConfig } from "../../dto/v1/company-setup/ServiceFrequencyDto";
import { ServiceFrequencyEntity } from "../../entity/v1";
import { IBuilder } from "./IBuilder";

export class ServiceFrequencyBuilder implements IBuilder<ServiceFrequencyEntity, ResponseServiceFrequencyDto> {
    private entity: ServiceFrequencyEntity;
    private dto: ResponseServiceFrequencyDto;

    constructor(){
        this.entity = new ServiceFrequencyEntity();
        this.dto = new ResponseServiceFrequencyDto();
    }

    setEntitySubscriptionTypeId(id: string): ServiceFrequencyBuilder {
        this.entity.id = id;
        return this;
    }

    setEntityCompanyId(companyId: number): ServiceFrequencyBuilder{
        this.entity.related_company_id = companyId;
        return this
    }

    fromDto(dto: CreateServiceFrequencyDto | UpdateServiceFrequencyDto): ServiceFrequencyBuilder {
        this.entity.name = dto?.name;
        this.entity.description = dto?.description;
        this.entity.frequency_name = dto.frequencyConfig.name;
        this.entity.frequency_interval = dto.frequencyConfig.defaultInterval;
        this.entity.frequency_count = dto.frequencyConfig.defaultCount;
        this.entity.type_of_cleaning = dto?.typeOfCleaning;
        this.entity.price = dto?.price;
        this.entity.discount = dto?.discount;
        this.entity.currency = dto?.currency;
        this.entity.is_active = dto?.isActive;
        return this;
    }
    fromEntity(entity: ServiceFrequencyEntity): ServiceFrequencyBuilder {
        this.dto.id = entity.id;
        this.dto.name = entity.name;
        this.dto.frequencyConfig = this.mapFreqEntityToDto(entity);
        this.dto.description = entity?.description;
        this.dto.typeOfCleaning = entity.type_of_cleaning
        this.dto.price = entity?.price;
        this.dto.discount = entity?.discount;
        this.dto.currency = entity?.currency;
        this.dto.isActive = entity?.is_active;
        
        return this;
    };

    private mapFreqEntityToDto(entity: ServiceFrequencyEntity): ServiceFrequencyConfig {
        const config = new ServiceFrequencyConfig();
        config.name = entity.frequency_name as ServiceFrequencyEnum;
        config.defaultInterval = entity.frequency_interval;
        config.defaultCount = entity.frequency_count;
        return config;
    }

    // private normalizeToRRule(frequency: ServiceRRuleFrequency): string {
    //     // Get the base frequency configuration
    //     const config = RRuleFrequency[frequency];
    //     if (!config) {
    //         throw new Error(`Invalid frequency type: ${frequency}`);
    //     }

    //     let rrule = `RRULE:FREQ=${config.rruleFreq};INTERVAL=${config.defaultInterval}`;
  
    //     // Add COUNT if specified in options or config
    //     if (config.defaultCount) {
    //         rrule += `;COUNT=${config.defaultCount}`;
    //     }
        
    //     // // Add UNTIL if specified
    //     // if (options?.until) {
    //     //     // Format date to RRULE format (YYYYMMDDTHHMMSSZ)
    //     //     const untilStr = options.until.toISOString()
    //     //     .replace(/[-:]/g, '')  // Remove dashes and colons
    //     //     .replace(/\.\d{3}/, '') // Remove milliseconds
    //     //     .replace(/Z$/, 'Z');    // Ensure Z is at the end
            
    //     //     rrule += `;UNTIL=${untilStr}`;
    //     // }
        
    //     // // Add day selection for weekly frequencies
    //     // if ((frequencyType === 'WEEKLY' || frequencyType === 'BIWEEKLY') && 
    //     //     options?.selectedDays?.length) {
    //     //     rrule += `;BYDAY=${options.selectedDays.join(',')}`;
    //     // }
        
    //     // // Add month day selection for monthly frequencies
    //     // if (frequencyType === 'MONTHLY' && options?.byMonthDay?.length) {
    //     //     rrule += `;BYMONTHDAY=${options.byMonthDay.join(',')}`;
    //     // }
        
    //     return rrule;

    // }

    buildEntity(): ServiceFrequencyEntity {
        return this.entity;
    }
    buildDto(): ResponseServiceFrequencyDto {
        return this.dto;
    }

}