import { CreateServiceAreaDto, ResponseServiceAreaDto, UpdateServiceAreaDto } from "../../dto/v1";
import { ServiceAreaEntity } from "../../entity/v1";
import { IBuilder } from "./IBuilder";

export class ServiceAreaBuilder implements IBuilder<ServiceAreaEntity, ResponseServiceAreaDto> {
    private entity: ServiceAreaEntity;
    private dto: ResponseServiceAreaDto;

    constructor(){
        this.entity = new ServiceAreaEntity();
        this.dto = new ResponseServiceAreaDto();
    }

    setCompanyId(companyId: number): ServiceAreaBuilder {
        this.entity.related_company_id = companyId;
        return this;
    }

    setServiceAreaId(serviceAreaId: string): ServiceAreaBuilder {
        this.entity.id = serviceAreaId;
        return this;
    }

    fromDto(dto: CreateServiceAreaDto | UpdateServiceAreaDto ): ServiceAreaBuilder {
        this.entity.sq_ft_from = dto?.sqFtFrom;
        this.entity.sq_ft_to = dto?.sqFtTo;
        this.entity.price = dto?.price;
        this.entity.is_active = dto?.isActive;
        this.entity.currency = dto?.currency;
        this.entity.type_of_cleaning = dto?.typeOfCleaning;
        return this;
    }

    fromEntity(entity: ServiceAreaEntity): ServiceAreaBuilder {
        this.dto.id = entity.id;
        this.dto.sqFtFrom = entity.sq_ft_from;
        this.dto.sqFtTo = entity.sq_ft_to;
        this.dto.price = entity.price;
        this.dto.currency = entity.currency;
        this.dto.typeOfCleaning = entity.type_of_cleaning;
        this.dto.isActive = entity.is_active;
        return this;
    }

    buildEntity(): ServiceAreaEntity {
        return this.entity;
    }
    buildDto(): ResponseServiceAreaDto {
        return this.dto;
    }

}