import { CreateJobExtraDto, ResponseJobExtraDto, UpdateJobExtraDto } from '../../dto/v1';
import { JobExtraEntity } from '../../entity/v1';
import { JobType } from '../../types';
import { IBuilder } from './IBuilder';

export class JobExtraBuilder implements IBuilder<JobExtraEntity, ResponseJobExtraDto>{
    private readonly entity: JobExtraEntity;
    private readonly dto: ResponseJobExtraDto;

    constructor(){
        this.entity = new JobExtraEntity();
        this.dto = new ResponseJobExtraDto();
    }

    setCompanyId(companyId: number): JobExtraBuilder {
        this.entity.related_company_id = companyId;
        return this;
    };

    setExtraId(id: string): JobExtraBuilder {
        this.entity.id = id;
        return this;
    }

    fromDto(dto: CreateJobExtraDto | UpdateJobExtraDto): JobExtraBuilder {
        this.entity.name = dto.name;
        this.entity.description = dto.description;
        this.entity.price = dto.price;
        this.entity.type_of_cleaning = dto.typeOfCleaning;
        this.entity.currency = dto.currency;
        this.entity.is_active = dto.isActive;
        this.entity.available_amount = dto.availableAmount;
        return this;
    }

    fromEntity(entity: JobExtraEntity): JobExtraBuilder {
        this.dto.id = entity.id;
        this.dto.name = entity.name;
        this.dto.description = entity.description;
        this.dto.typeOfCleaning = entity.type_of_cleaning;
        this.dto.price = entity.price;
        this.dto.currency = entity.currency;
        this.dto.availableAmount = entity.available_amount;
        this.dto.isActive = entity.is_active;
        return this;
    }

    buildEntity(): JobExtraEntity {
        return this.entity;
    }
    buildDto(): ResponseJobExtraDto {
        return this.dto;
    }

}