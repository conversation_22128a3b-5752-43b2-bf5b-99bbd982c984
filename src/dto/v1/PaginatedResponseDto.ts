import { Expose, Type } from "class-transformer";
import { IsInt, IsNotEmpty, IsOptional, Min, ValidateNested } from "class-validator";

class PaginationMetaDto {
    @Expose()
    @IsInt()
    @Min(0)
    offset: number;
    @Expose()
    @IsInt()
    @Min(1)
    limit: number;
    @Expose()
    @IsInt()
    @Min(0)
    totalItems: number;
    @Expose()
    @IsInt()
    @Min(1)
    totalPages: number;

    constructor(offset: number, limit: number, totalItems: number) {
        this.offset = offset;
        this.limit = limit;
        this.totalItems = totalItems;
        this.totalPages = Math.ceil(totalItems / limit);
      }
}


export default class PaginatedResponseDto<T>{
    @Expose()
    @IsNotEmpty()
    readonly message: string;

    @Expose()
    @IsOptional()
    readonly data: T[];

    @Expose()
    @IsOptional()
    @ValidateNested()
    @Type(() => PaginationMetaDto)
    readonly metadata?: PaginationMetaDto;

    constructor(message: string, data?: T[], offset?: number, limit?: number, totalItems?: number){
        this.message = message;
        this.data = data;
        this.metadata = offset || limit ? new PaginationMetaDto(offset, limit, totalItems) : undefined;
    }
}