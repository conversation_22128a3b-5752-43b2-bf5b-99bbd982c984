import { Transform } from "class-transformer";
import { IsNotEmpty, IsNumber, IsObject, IsOptional, IsString } from "class-validator";

export class InvitationCodeRequestDto {
    @IsString()
    @IsNotEmpty()
    @Transform(({ value }) => (typeof value === 'string' ? value.trim() : value))
    role: string
}

export class InvitationCodeResponseDto {
    @IsString()
    @IsNotEmpty()
    message: string;

    @IsObject()
    data: {};
}
