import { Expose } from "class-transformer";
import { IsBoolean, IsEnum, IsNumber, IsOptional, IsString } from "class-validator";
import { CurrencyType, JobType } from "../../../types";


export class CreateJobExtraDto {

    @Expose()
    @IsString()
    name: string;

    @Expose()
    @IsString()
    @IsOptional()
    description: string;

    @Expose()
    @IsNumber()
    price: number;

    @Expose()
    @IsEnum(CurrencyType)
    currency: CurrencyType

    @Expose()
    @IsEnum(JobType)
    typeOfCleaning: JobType

    @Expose()
    @IsBoolean()
    isActive: boolean;

    @Expose()
    @IsNumber()
    availableAmount: number;
}

export class UpdateJobExtraDto {

    @Expose()
    @IsString()
    @IsOptional()
    name: string;

    @Expose()
    @IsString()
    @IsOptional()
    description: string;

    @Expose()
    @IsNumber()
    @IsOptional()
    price: number;

    @Expose()
    @IsEnum(CurrencyType)
    @IsOptional()
    currency: CurrencyType

    @Expose()
    @IsEnum(JobType)
    @IsOptional()
    typeOfCleaning: JobType

    @Expose()
    @IsBoolean()
    @IsOptional()
    isActive: boolean;

    @Expose()
    @IsNumber()
    @IsOptional()
    availableAmount: number;
}

export class ResponseJobExtraDto {

    @Expose()
    @IsOptional()
    id: string;

    @Expose()
    @IsOptional()
    name: string;

    @Expose()
    @IsOptional()
    description: string;

    @Expose()
    @IsOptional()
    price: number;

    @Expose()
    @IsEnum(CurrencyType)
    @IsOptional()
    currency: CurrencyType

    @Expose()
    @IsEnum(JobType)
    @IsOptional()
    typeOfCleaning: JobType

    @Expose()
    @IsBoolean()
    @IsOptional()
    isActive: boolean;

    @Expose()
    @IsNumber()
    @IsOptional()
    availableAmount: number;
}