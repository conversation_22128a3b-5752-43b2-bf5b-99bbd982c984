import { Expose } from "class-transformer";
import { IsNotEmpty, IsNumber, IsString, IsEnum, IsBoolean, IsOptional, IsInt } from "class-validator";
import { JobType, CurrencyType } from "../../../types";

export class CreateServiceAreaDto {
    
    @Expose()
    @IsInt()
    sqFtFrom: number

    @Expose()
    @IsInt()
    sqFtTo: number

    @Expose()
    @IsEnum(JobType)
    typeOfCleaning: JobType

    @Expose()
    @IsNumber()
    price: number

    @Expose()
    @IsEnum(CurrencyType)
    currency: CurrencyType

    @Expose()
    @IsBoolean()
    isActive: boolean
}

export class UpdateServiceAreaDto {

    @Expose()
    @IsOptional()
    @IsInt()
    sqFtFrom: number

    @Expose()
    @IsOptional()
    @IsInt()
    sqFtTo: number

    @Expose()
    @IsOptional()
    @IsEnum(JobType)
    typeOfCleaning: JobType

    @Expose()
    @IsOptional()
    @IsNumber()
    price: number

    @Expose()
    @IsOptional()
    @IsEnum(CurrencyType)
    currency: CurrencyType

    @Expose()
    @IsOptional()
    @IsBoolean()
    isActive: boolean
}

export class ResponseServiceAreaDto {
    @Expose()
    @IsString()
    id: string

    @Expose()
    @IsInt()
    sqFtFrom: number

    @Expose()
    @IsInt()
    sqFtTo: number

    @Expose()
    @IsEnum(JobType)
    typeOfCleaning?: JobType

    @Expose()
    @IsNumber()
    price?: number

    @Expose()
    @IsEnum(CurrencyType)
    currency?: CurrencyType

    @Expose()
    @IsBoolean()
    isActive?: boolean
}