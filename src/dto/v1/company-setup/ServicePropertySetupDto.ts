import { IsBoolean, IsEnum, IsN<PERSON>ber, IsOptional, IsString } from "class-validator";
import { CurrencyType, JobType } from "../../../types";
import { Expose } from "class-transformer";

export class CreateServicePropertySetupDto{
    @Expose()
    @IsString()
    name: string;

    @Expose()
    @IsOptional()
    @IsString()
    description: string;

    @Expose()
    @IsNumber()
    available_amount: number;

    @Expose()
    @IsEnum(JobType)
    type_of_cleaning: JobType;

    @Expose()
    @IsNumber()
    price: number;

    @Expose()
    @IsEnum(CurrencyType)
    currency: CurrencyType;

    @Expose()
    @IsBoolean()
    is_active: boolean;
}

export class UpdateServicePropertySetupDto{
    @Expose()
    @IsOptional()
    @IsString()
    name: string;

    @Expose()
    @IsOptional()
    @IsString()
    description: string;

    @Expose()
    @IsOptional()
    @IsNumber()
    available_amount: number;

    @Expose()
    @IsOptional()
    @IsEnum(JobType)
    type_of_cleaning: JobType;

    @Expose()
    @IsOptional()
    @IsNumber()
    price: number;

    @Expose()
    @IsOptional()
    @IsEnum(CurrencyType)
    currency: CurrencyType;

    @Expose()
    @IsOptional()
    @IsBoolean()
    is_active: boolean;
}

export class ResponseServicePropertySetupDto{
    @Expose()
    @IsString()
    id: string;

    @Expose()
    @IsString()
    name: string;

    @Expose()
    @IsOptional()
    @IsString()
    description?: string;

    @Expose()
    @IsOptional()
    @IsNumber()
    available_amount?: number;

    @Expose()
    @IsOptional()
    @IsEnum(JobType)
    type_of_cleaning?: JobType;

    @Expose()
    @IsOptional()
    @IsNumber()
    price?: number;

    @Expose()
    @IsOptional()
    @IsEnum(CurrencyType)
    currency?: CurrencyType;

    @Expose()
    @IsOptional()
    @IsBoolean()
    is_active?: boolean;
}