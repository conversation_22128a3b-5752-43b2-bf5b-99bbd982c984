import { Expose, Type } from "class-transformer"
import { IsArray, IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateNested } from "class-validator"
import { JobType, CurrencyType } from "../../../types"
import { CreateAdditionalDetailsChoicesDto, ResponseAdditionalDetailsChoicesDto, UpdateAdditionalDetailsChoicesDto } from "./AdditionalDetailsChoicesDto"

export class CreateAdditionalDetailsDto {
    
    @Expose()
    @IsString()
    name: string

    @Expose()
    @IsOptional()
    @IsString()
    description: string

    @Expose()
    @IsEnum(JobType)
    typeOfCleaning: JobType

    @Expose()
    @IsBoolean()
    isActive: boolean

    @Expose()
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateAdditionalDetailsChoicesDto)
    choices: CreateAdditionalDetailsChoicesDto[];
}

export class UpdateAdditionalDetailsDto {
    
    @Expose()
    @IsOptional()
    @IsString()
    name?: string

    @Expose()
    @IsOptional()
    @IsString()
    description?: string

    @Expose()
    @IsOptional()
    @IsEnum(JobType)
    typeOfCleaning?: JobType

    @Expose()
    @IsOptional()
    @IsBoolean()
    isActive?: boolean

    @Expose()
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => UpdateAdditionalDetailsChoicesDto)
    choices?: UpdateAdditionalDetailsChoicesDto[];
}

export class ResponseAdditionalDetailsDto {
    @Expose()
    @IsString()
    id: string
    
    @Expose()
    @IsString()
    name: string

    @Expose()
    @IsString()
    description: string

    @Expose()
    @IsEnum(JobType)
    typeOfCleaning: JobType

    @Expose()
    @IsBoolean()
    isActive: boolean

    @Expose()
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ResponseAdditionalDetailsChoicesDto)
    choices: ResponseAdditionalDetailsChoicesDto[];
}