import { CurrencyType } from "../../../types";
import { Expose, Type } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateAdditionalDetailsChoicesDto {
    @Expose()
    @IsString()
    name: string;

    @Expose()
    @IsNumber({ maxDecimalPlaces: 2 })
    price: number;

    @Expose()
    @IsEnum(CurrencyType)
    currency: CurrencyType;
}

export class UpdateAdditionalDetailsChoicesDto {

    @Expose()
    @IsOptional()
    @IsString()
    detailChoiceId?: string;

    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @Expose()
    @IsOptional()
    @IsNumber({ maxDecimalPlaces: 2 })
    price?: number;

    @Expose()
    @IsOptional()
    @IsEnum(CurrencyType)
    currency?: CurrencyType;
}

export class ResponseAdditionalDetailsChoicesDto {
    @Expose()
    @IsString()
    id: string;

    @Expose()
    @IsString()
    name: string;

    @Expose()
    @IsNumber({ maxDecimalPlaces: 2 })
    price: number;
    
    @Expose()
    @IsEnum(CurrencyType)
    currency: CurrencyType;
}