import { IsBoolean, IsEnum, IsNot<PERSON>mpty, <PERSON><PERSON><PERSON>ber, <PERSON>Optional, IsString } from "class-validator";
import { CurrencyType, JobType } from "../../../types";
import { Expose } from "class-transformer";

export class CreateTypeOfServiceDto {
    
    @Expose()
    @IsString()
    name: string

    @Expose()
    @IsString()
    description: string;

    @Expose()
    @IsEnum(JobType)
    typeOfCleaning: JobType

    @Expose()
    @IsNumber()
    price: number

    @Expose()
    @IsEnum(CurrencyType)
    currency: CurrencyType

    @Expose()
    @IsBoolean()
    isActive: boolean
}

export class UpdateTypeOfServiceDto {
    
    @Expose()
    @IsOptional()
    @IsString()
    name: string

    @Expose()
    @IsOptional()
    @IsString()
    description: string;

    @Expose()
    @IsOptional()
    @IsEnum(JobType)
    typeOfCleaning: JobType

    @Expose()
    @IsOptional()
    @IsNumber()
    price: number

    @Expose()
    @IsOptional()
    @IsEnum(CurrencyType)
    currency: CurrencyType

    @Expose()
    @IsOptional()
    @IsBoolean()
    isActive: boolean
}

export class ResponseTypeOfServiceDto {

    @Expose()
    @IsString()
    id: string
    
    @Expose()
    @IsString()
    name: string

    @Expose()
    @IsString()
    description?: string;

    @Expose()
    @IsEnum(JobType)
    typeOfCleaning?: JobType

    @Expose()
    @IsNumber()
    price?: number

    @Expose()
    @IsEnum(CurrencyType)
    currency?: CurrencyType

    @Expose()
    @IsBoolean()
    isActive?: boolean
}