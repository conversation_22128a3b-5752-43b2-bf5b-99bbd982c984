import { Expose, Type } from "class-transformer";
import { IsString, Is<PERSON>ptional, IsNumber, IsEnum, Min, Max, IsBoolean, IsInt, ValidateNested } from "class-validator";
import { CurrencyType, JobType } from "../../../types";
import { ServiceFrequencyEnum } from "../../../constants/types";

export class ServiceFrequencyConfig {
    @Expose()
    @IsEnum(ServiceFrequencyEnum)
    name: ServiceFrequencyEnum | string;
    @Expose()
    @IsNumber()
    @IsOptional()
    defaultInterval?: number;
    @Expose()
    @IsNumber()
    @IsOptional()
    defaultCount?: number;
}


export class CreateServiceFrequencyDto {
    @Expose()
    @IsString()
    name: string;

    @Expose()
    @IsOptional()
    @IsString()
    description: string;

    @Expose()
    @ValidateNested()
    @Type(() => ServiceFrequencyConfig)
    frequencyConfig: ServiceFrequencyConfig

    @Expose()
    @IsEnum(JobType)
    typeOfCleaning: JobType;

    @Expose()
    @IsNumber()
    price: number;

    @Expose()
    @IsInt({ message: 'Percentage must be a number' })
    @Min(0, { message: 'Percentage cannot be less than 0' })
    @Max(100, { message: 'Percentage cannot be greater than 100' })
    discount: number;

    @Expose()
    @IsEnum(CurrencyType)
    currency: CurrencyType;
    
    @Expose()
    @IsBoolean()
    isActive: boolean;
}

export class UpdateServiceFrequencyDto {
    @Expose()
    @IsString()
    @IsOptional()
    name?: string;

    @Expose()
    @IsOptional()
    @IsString()
    description?: string;

    @Expose()
    @ValidateNested()
    @IsOptional()
    @Type(() => ServiceFrequencyConfig)
    frequencyConfig?: ServiceFrequencyConfig;

    @Expose()
    @IsEnum(JobType)
    @IsOptional()
    typeOfCleaning?: JobType;

    @Expose()
    @IsNumber()
    @IsOptional()
    price?: number;

    @Expose()
    @IsOptional()
    @IsInt({ message: 'Percentage must be a number' })
    @Min(0, { message: 'Percentage cannot be less than 0' })
    @Max(100, { message: 'Percentage cannot be greater than 100' })
    discount?: number;

    @Expose()
    @IsOptional()
    @IsEnum(CurrencyType)
    currency?: CurrencyType;
    
    @Expose()
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;
}

export class ResponseServiceFrequencyDto {
    @Expose()
    @IsString()
    id: string;

    @Expose()
    @IsString()
    name: string;

    @Expose()
    @IsOptional()
    @IsString()
    description?: string;

    @Expose()
    @ValidateNested()
    @Type(() => ServiceFrequencyConfig)
    frequencyConfig: ServiceFrequencyConfig;

    @Expose()
    @IsEnum(JobType)
    typeOfCleaning?: JobType;

    @Expose()
    @IsNumber()
    price?: number;

    @Expose()
    @IsInt()
    discount?: number;

    @Expose()
    @IsEnum(CurrencyType)
    currency?: CurrencyType;
    
    @Expose()
    @IsBoolean()
    isActive?: boolean;
}