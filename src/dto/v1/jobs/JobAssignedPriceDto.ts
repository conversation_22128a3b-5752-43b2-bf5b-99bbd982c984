export class ResponseJobAssignedPrice {

}

export class CreateJobPriceDTO {
    jobId?: string;
    serviceTypePrice: number;
    serviceAreaPrice: number;
    serviceDetailsPrices: {
        amount: number;
        price: number;
    } [];
    extrasPrices: {
        amount: number;
        price: number;
    } [];
    serviceFreqPrice: number;
    serviceFreqDiscount: number;
    additionalDetailPrices: number[];
    locationTaxes: {
        federal: number;
        state: number;
    }
}

export class UpdateJobPriceDTO {
    jobId?: string;
    serviceTypePrice: number;
    serviceAreaPrice: number;
    serviceDetailsPrices: {
        amount: number;
        price: number;
    } [];
    extrasPrices: {
        amount: number;
        price: number;
    } [];
    serviceFreqPrice: number;
    serviceFreqDiscount: number;
    additionalDetailPrices: number[];
    locationTaxes: {
        federal: number;
        state: number;
    }
}