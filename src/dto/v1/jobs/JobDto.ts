
import { ClientProfileDto, EmployeeProfileDto } from "../profile/ProfileDto";
import { IsOptional, IsNumber, IsString, IsArray, ValidateNested, IsEnum } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { ResponseServiceAreaDto, ResponseTypeOfServiceDto } from "..";
import { JobStatus, JobType } from "../../../types/types";

export class ResidentialCleaningJobDetailsDto {
    @Expose()
    @IsString()
    id: string;

    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @Expose()
    @IsNumber()
    amount: number;
}

export class ResidentialCleaningJobExtraDto {
    @Expose()
    @IsString()
    id: string;

    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @Expose()
    @IsNumber()
    amount: number;
}

export class ResidentialCleaningAdditionalDetailsDto {
    @Expose()
    @IsString()
    id: string;

    @Expose()
    @IsOptional()
    @IsString()
    name?: string;

    @Expose()
    @IsString()
    choiceId: string;

    @Expose()
    @IsOptional()
    @IsString()
    choiceName?: string;
}

export class CreateResidentialCleaningJobDto {

    @Expose()
    @IsEnum(JobType)
    type: JobType;

    @Expose()
    @IsOptional()
    @IsString()
    startTime?: Date;

    @Expose()
    @IsOptional()
    @IsString()
    endTime?: Date;

    @Expose()
    @IsEnum(JobStatus)
    status?: JobStatus;

    @Expose()
    @IsString()
    serviceArea: string;

    @Expose()
    @IsArray()
    @ValidateNested({each: true})
    @Type(() => ResidentialCleaningJobDetailsDto)
    serviceDetails: ResidentialCleaningJobDetailsDto[];

    @Expose()
    @IsString()
    serviceType: string;

    @Expose()
    @IsString()
    serviceFrequency: string;

    @Expose()
    @IsArray()
    @IsOptional()
    @ValidateNested({each: true})
    @Type(() => ResidentialCleaningJobExtraDto)
    extras?: ResidentialCleaningJobExtraDto[];

    @Expose()
    @IsArray()
    @IsOptional()
    @ValidateNested({each: true})
    @Type(() => ResidentialCleaningAdditionalDetailsDto)
    additionalDetails?: ResidentialCleaningAdditionalDetailsDto[];

    @Expose()
    @ValidateNested()
    @Type(() => ClientProfileDto)
    client?: ClientProfileDto;

    @Expose()
    @IsArray()
    @IsOptional()
    @ValidateNested()
    @Type(() => EmployeeProfileDto)
    assignedEmployees?: EmployeeProfileDto[];
}

export class UpdateResidentialCleaningJobDto {
    @Expose()
    @IsOptional()
    @IsEnum(JobType)
    type: JobType;

    @Expose()
    @IsOptional()
    @IsString()
    startTime?: Date;

    @Expose()
    @IsOptional()
    @IsString()
    endTime?: Date;

    @Expose()
    @IsOptional()
    @IsEnum(JobStatus)
    status?: JobStatus;

    @Expose()
    @IsOptional()
    @IsString()
    serviceArea?: string;

    @Expose()
    @IsOptional()
    @IsArray()
    @ValidateNested({each: true})
    @Type(() => ResidentialCleaningJobDetailsDto)
    serviceDetails?: ResidentialCleaningJobDetailsDto[];

    @Expose()
    @IsOptional()
    @IsString()
    serviceType?: string;

    @Expose()
    @IsOptional()
    @IsString()
    serviceFrequency?: string;

    @Expose()
    @IsArray()
    @IsOptional()
    @ValidateNested({each: true})
    @Type(() => ResidentialCleaningJobExtraDto)
    extras?: ResidentialCleaningJobExtraDto[];

    @Expose()
    @IsArray()
    @IsOptional()
    @ValidateNested({each: true})
    @Type(() => ResidentialCleaningAdditionalDetailsDto)
    additionalDetails?: ResidentialCleaningAdditionalDetailsDto[];

    @Expose()
    @IsOptional()
    client?: ClientProfileDto;

    @Expose()
    @IsOptional()
    assignedEmployees?: EmployeeProfileDto[];
}

// export class ResponseScheduledJobDto {

//     @Expose()
//     @IsOptional()
//     @IsString()
//     id: string;

//     @Expose()
//     @IsOptional()
//     @IsString()
//     startTime?: Date;

//     @Expose()
//     @IsOptional()
//     @IsString()
//     endTime?: Date;

//     @Expose()
//     @IsOptional()
//     @IsString()
//     status?: JobStatus;

//     @Expose()
//     @IsArray()
//     @ValidateNested()
//     @Type(() => ResidentialCleaningJobDetailsDto)
//     serviceDetails?: ResidentialCleaningJobDetailsDto[];

//     @Expose()
//     @ValidateNested()
//     @Type(() => ResponseTypeOfServiceDto)
//     serviceType?: ResponseTypeOfServiceDto;

//     @Expose()
//     @ValidateNested()
//     @Type(() => ResponseServiceAreaDto)
//     serviceArea?: ResponseServiceAreaDto;

//     @Expose()
//     @IsOptional()
//     @IsArray()
//     @ValidateNested()
//     @Type(() => ResidentialCleaningJobExtraDto)
//     extras?: ResidentialCleaningJobExtraDto[];

//     @Expose()
//     @IsArray()
//     @IsOptional()
//     @ValidateNested()
//     @Type(() => ResidentialCleaningAdditionalDetailsDto)
//     additionalDetails?: ResidentialCleaningAdditionalDetailsDto[];

//     @Expose()
//     @IsOptional()
//     assignedEmployees?: EmployeeProfileDto[];

//     @Expose()
//     @IsOptional()
//     totalPrice: number;
// }

export class ResponseScheduledJobDto {

    @Expose()
    @IsOptional()
    @IsString()
    id: string;

    @Expose()
    @IsOptional()
    @IsString()
    jobId: string;

    @Expose()
    @IsOptional()
    @IsString()
    startTime?: Date;

    @Expose()
    @IsOptional()
    @IsString()
    endTime?: Date;

    @Expose()
    @IsOptional()
    @IsString()
    status?: JobStatus;

    @Expose()
    @IsOptional()
    totalPrice: number;

    @Expose()
    @ValidateNested()
    @Type(() => ResponseTypeOfServiceDto)
    serviceFrequency?: ResponseTypeOfServiceDto;

    @Expose()
    @IsArray()
    @ValidateNested()
    @Type(() => ResidentialCleaningJobDetailsDto)
    serviceDetails?: ResidentialCleaningJobDetailsDto[];

    @Expose()
    @ValidateNested()
    @Type(() => ResponseTypeOfServiceDto)
    serviceType?: ResponseTypeOfServiceDto;

    @Expose()
    @ValidateNested()
    @Type(() => ResponseServiceAreaDto)
    serviceArea?: ResponseServiceAreaDto;

    @Expose()
    @IsOptional()
    @IsArray()
    @ValidateNested()
    @Type(() => ResidentialCleaningJobExtraDto)
    extras?: ResidentialCleaningJobExtraDto[];

    @Expose()
    @IsArray()
    @IsOptional()
    @ValidateNested()
    @Type(() => ResidentialCleaningAdditionalDetailsDto)
    additionalDetails?: ResidentialCleaningAdditionalDetailsDto[];

    @Expose()
    @IsOptional()
    assignedEmployees?: EmployeeProfileDto[];

    @Expose()
    @IsOptional()
    client?: ClientProfileDto;
}