import { Expose, Type } from "class-transformer";
import { ValidateNested } from "class-validator";
import { ResponseServiceAreaDto, ResponseServicePropertySetupDto, ResponseTypeOfServiceDto } from "..";

export class ResponseJobSpecificationDto{ 

    @Expose()
    @ValidateNested()
    @Type(() => ResponseServicePropertySetupDto)
    serviceDetails: ResponseServicePropertySetupDto[];

    @Expose()
    @ValidateNested()
    @Type(() => ResponseTypeOfServiceDto)
    typeOfServices: ResponseTypeOfServiceDto[];

    @Expose()
    @ValidateNested()
    @Type(() => ResponseServiceAreaDto)
    serviceAreas: ResponseServiceAreaDto[];
}