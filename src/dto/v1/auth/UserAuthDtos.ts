import { IsNotEmpty, IsObject, IsString, Length } from "class-validator";

export class UserLoginDto {
    @IsString()
    @IsNotEmpty()
    @Length(4)
    username: string;
    
    @IsString()
    @IsNotEmpty()
    @Length(4)
    password: string;
}

export class UserRegisterDto {
    @IsString()
    @IsNotEmpty()
    @Length(4)
    username: string;
    
    @IsString()
    @IsNotEmpty()
    @Length(4)
    password: string;

    @IsString()
    @IsNotEmpty({message: 'Invitation code must be present'})
    invitation_code: string;
}

export class UserAuthResponseDto {

    @IsString()
    message?: string;

    @IsObject()
    @IsNotEmpty()
    data: {
        token: string
    };
}

export class UserTokenDto {
    @IsString()
    @IsNotEmpty()
    accessToken: string;
    @IsString()
    @IsNotEmpty()
    refreshToken: string;
}