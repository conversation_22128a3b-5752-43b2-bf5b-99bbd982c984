import { IsEnum, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, Length, ValidateIf, ValidateNested, } from "class-validator";
import { ProfileUserTypes } from "../../../constants";
import { Transform, Type } from "class-transformer";

export class AddressInfoDto {
    @IsString()
    @IsOptional()
    street?: string;

    @IsString()
    @IsOptional()
    city?: string;

    @IsString()
    @IsOptional()
    state?: string;

    @IsString()
    @IsOptional()
    postalCode?: string;
}

export class CompanyInfoDto{
    @IsNumber()
    @IsOptional()
    companyId: number;

    @IsString()
    @IsOptional()
    companyName: string;

    @IsString()
    @IsOptional()
    companyWebsite: string;

    @IsString()
    @IsOptional()
    companyPhone: string;

    @IsString()
    @IsOptional()
    companyEmail: string;

    @ValidateNested()
    @Type(() => AddressInfoDto)
    @IsOptional()
    addressInfo: AddressInfoDto;
}

export class ProfileCredentialDto {
    @IsString()
    @IsNotEmpty()
    @Length(4)
    username: string;
    
    @IsString()
    @IsNotEmpty()
    @Length(4)
    password: string;
}

export class AdminProfileDto {

    @ValidateNested()
    @Type(() => ProfileCredentialDto)
    @IsOptional()
    credentials?: ProfileCredentialDto

    @IsString()
    @IsNotEmpty({message: 'Invitation code must be present'})
    @IsOptional()
    invitationCode?: string;

    @IsNumber()
    @IsOptional()
    profileId?: number;

    @IsString()
    @IsOptional()
    firstName: string;

    @IsString()
    @IsOptional()
    lastName: string;

    @IsString()
    @IsOptional()
    phoneNumber: string;

    @IsString()
    @IsOptional()
    email: string;

    @ValidateNested()
    @Type(() => CompanyInfoDto)
    @IsOptional()
    companyInfo: CompanyInfoDto;

    @IsEnum(ProfileUserTypes)
    @IsOptional()
    position?: ProfileUserTypes;
}

export class EmployeeProfileDto {

    @ValidateNested()
    @Type(() => ProfileCredentialDto)
    @IsOptional()
    credentials?: ProfileCredentialDto

    @IsNumber()
    @IsOptional()
    profileId?: number;

    @IsString()
    @IsOptional()
    firstName?: string;

    @IsString()
    @IsOptional()
    lastName?: string;

    @IsString()
    @IsOptional()
    phoneNumber?: string;

    @IsString()
    @IsOptional()
    email?: string;

    @IsObject()
    @IsOptional()
    addressInfo?: AddressInfoDto;

    @IsString()
    @IsOptional()
    dateOfBirth?: string;

    @IsEnum(ProfileUserTypes)
    @IsOptional()
    position?: ProfileUserTypes;

}

export class ClientProfileDto{

    @ValidateNested()
    @Type(() => ProfileCredentialDto)
    @IsOptional()
    credentials?: ProfileCredentialDto

    @IsNumber()
    @IsOptional()
    profileId?: number;

    @IsString()
    @IsOptional()
    firstName?: string;

    @IsString()
    @IsOptional()
    lastName?: string;

    @IsString()
    @IsOptional()
    phoneNumber?: string;

    @IsString()
    @IsOptional()
    email?: string;

    @IsString()
    @IsOptional()
    companyName?: string;
    
    @IsObject()
    @IsOptional()
    addressInfo?: AddressInfoDto;
}

export class AdminProfileResponseDto {
    @IsString()
    @IsOptional()
    message: string;

    @IsString()
    @IsOptional()
    accessToken?: string;
    
    @ValidateNested()
    @Type(() => AdminProfileDto)
    @IsOptional()
    data: AdminProfileDto;
}

export class EmployeeProfileResponseDto {
    @IsString()
    @IsOptional()
    message: string;

    @IsString()
    @IsOptional()
    accessToken?: string;
    
    @ValidateNested()
    @Type(() => EmployeeProfileDto)
    @IsOptional()
    data: EmployeeProfileDto | EmployeeProfileDto[];
}

export class ClientProfileResponseDto {
    @IsString()
    @IsOptional()
    message: string;
    
    @ValidateNested()
    @Type(() => ClientProfileDto)
    @IsOptional()
    data: ClientProfileDto | ClientProfileDto[];
}