import { Logging } from "../../util";
import { CronWorker } from "./CronWorker";

export class CronWorkerManager {

    constructor(private readonly logger: Logging = new Logging()){

    }
    private workers: Map<string, CronWorker> = new Map();
    
    /**
     * Register a worker with the manager
     */
    public registerWorker(worker: CronWorker): void {
      this.workers.set(worker.constructor.name, worker);
    }
    
    /**
     * Start all registered workers
     */
    public startAll(): void {
      for (const worker of this.workers.values()) {
        worker.start();
      }
    }
    
    /**
     * Stop all registered workers
     */
    public stopAll(): void {
      for (const worker of this.workers.values()) {
        worker.stop();
      }
    }
    
    /**
     * Start a specific worker by name
     */
    public startWorker(name: string): void {
      const worker = this.workers.get(name);
      if (worker) {
        worker.start();
      } else {
        this.logger.error(`Worker ${name} not found`);
      }
    }
    
    /**
     * Stop a specific worker by name
     */
    public stopWorker(name: string): void {
      const worker = this.workers.get(name);
      if (worker) {
        worker.stop();
      } else {
        this.logger.error(`Worker ${name} not found`);
      }
    }
    
    /**
     * Run a specific worker immediately
     */
    public async runWorkerImmediate(name: string): Promise<void> {
      const worker = this.workers.get(name);
      if (worker) {
        await worker.runImmediate();
      } else {
        this.logger.error(`Worker ${name} not found`);
      }
    }
  }