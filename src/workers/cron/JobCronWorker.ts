import { CronWorker } from "./CronWorker";
import { container } from '../../configs/container';
import { JobService } from "../../services";
import { Logging } from "../../util";
import * as dotenv from 'dotenv'
import { AppDatabaseClient } from "../../configs";
import { ScheduledJobDetailsEntity } from "../../entity/v1";

dotenv.config()

export class JobCronWorker extends CronWorker {
    private jobService: JobService;
    private readonly BATCH_SIZE: number;
    private readonly NUM_MONTHS: number;

    constructor(
      cronExpression: string = '0 0 * * *', // Daily at midnight
      logger: Logging = new Logging(),
      initialDelay: number = 5 * 60 * 1000 // 5 minutes delay
    ) {
      super('JobCronWorker', cronExpression, logger, initialDelay);
      this.jobService = container.resolve('jobService');
      this.BATCH_SIZE = Number(process.env.JOB_BATCH_SIZE || 20);
      this.NUM_MONTHS = Number(process.env.JOB_GENERATED_NUM_MONTHS || 3)
    }

    async initialize() {
      // Wait for database connection before initializing the service
      await AppDatabaseClient.initialize();
      this.jobService = container.resolve('jobService');
    }


    protected async processBatch(): Promise<void> {
      if (!this.jobService) {
        await this.initialize();
      }

      let offset = 0;
      let hasMoreJobs = true;

      this.logger.info('Starting job maintenance processing');

      while (hasMoreJobs) {
        try {
          // Fetch a batch of job entities
          const jobEntityBatch = await this.jobService.findJobsByOffset(offset, this.BATCH_SIZE, this.NUM_MONTHS);

          // If no more jobs, exit the loop
          if (jobEntityBatch.length === 0) {
              hasMoreJobs = false;
              break;
          }

          // Get all job IDs from the batch
          const jobIds = jobEntityBatch.map(job => job.id);

          // Fetch all last scheduled jobs for the batch in a single query
          const lastScheduledJobs = await this.jobService.findLastScheduledJobsByIds(jobIds);

          // Create a map for quick lookup
          const lastScheduledJobMap = new Map<string, ScheduledJobDetailsEntity>();
          lastScheduledJobs.forEach((job: ScheduledJobDetailsEntity) => {
              if (job) {
                  lastScheduledJobMap.set(job.related_job_id, job);
              }
          });

          // Prepare arrays for batch operations
          const allScheduledJobs = [];
          const jobsToUpdate = [];

          // Process each job in the batch
          for (const jobEntity of jobEntityBatch) {
              // Skip inactive jobs
              if (!jobEntity.is_active) {
                  this.logger.debug(`Skipping inactive job ${jobEntity.id}`);
                  continue;
              }

              // Get the last scheduled job for this job entity from the map
              const lastScheduledJob = lastScheduledJobMap.get(jobEntity.id) || null;

              try {
                  // Generate scheduled jobs for this job entity
                  const scheduledJobs = this.jobService.generateScheduledJobs(jobEntity, lastScheduledJob, this.NUM_MONTHS);

                  // Add to the batch of scheduled jobs to create
                  if (scheduledJobs.length > 0) {
                      allScheduledJobs.push(...scheduledJobs);
                  }

                  // Mark job for update with current timestamp
                  jobEntity.last_job_generated_datetime = new Date();
                  jobsToUpdate.push(jobEntity);
              } catch (error) {
                  this.logger.error(`Error processing job ${jobEntity.id}:`, error.message);
                  // Continue with other jobs even if one fails
              }
          }

          // Save generated scheduled jobs in manageable chunks
          if (allScheduledJobs.length > 0) {
              const CHUNK_SIZE = 100; // Adjust based on your system's performance characteristics
              this.logger.info(`Processing ${allScheduledJobs.length} scheduled jobs in chunks of ${CHUNK_SIZE}`);

              // Process in chunks to avoid overwhelming the database
              for (let i = 0; i < allScheduledJobs.length; i += CHUNK_SIZE) {
                  const chunk = allScheduledJobs.slice(i, i + CHUNK_SIZE);
                  this.logger.debug(`Processing chunk ${Math.floor(i/CHUNK_SIZE) + 1} with ${chunk.length} jobs`);
                  await this.jobService.createScheduledJobsBatch(chunk);
              }
          }

          // Update job entities' last generated time in manageable chunks
          if (jobsToUpdate.length > 0) {
              const CHUNK_SIZE = 100; // Adjust based on your system's performance characteristics
              this.logger.info(`Updating ${jobsToUpdate.length} jobs in chunks of ${CHUNK_SIZE}`);

              // Process in chunks to avoid overwhelming the database
              for (let i = 0; i < jobsToUpdate.length; i += CHUNK_SIZE) {
                  const chunk = jobsToUpdate.slice(i, i + CHUNK_SIZE);
                  this.logger.debug(`Updating chunk ${Math.floor(i/CHUNK_SIZE) + 1} with ${chunk.length} jobs`);
                  await this.jobService.updateJobInfoLastGeneratedTimeByIdBatch(chunk);
              }
          }

          // Move to the next batch
          offset += this.BATCH_SIZE;

          // Small delay between batches
          if (hasMoreJobs) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        } catch (error) {
          this.logger.error('Error processing job batch:', error.message);
          hasMoreJobs = false;
        }
      }
    }
}
