import * as cron from 'node-cron';
import { Logging } from '../../util';

export abstract class CronWorker {
  protected logger: Logging;
  protected cronExpression: string;
  protected name: string;
  private cronJob: cron.ScheduledTask;
  private isRunning: boolean = false;
  private initialDelay: number; // in milliseconds

  constructor(
    name: string, 
    cronExpression: string = '0 0 * * *', 
    logger: Logging,
    initialDelay: number = 5 * 60 * 1000 // 5 minutes default delay
  ) {
    this.name = name;
    this.cronExpression = cronExpression;
    this.logger = logger;
    this.initialDelay = initialDelay;
  }

  /**
   * The main logic to be implemented by concrete workers
   */
  protected abstract processBatch(): Promise<void>;

  /**
   * Start the worker with the specified cron schedule
   */
  public start(): void {
    this.logger.info(`Starting worker: ${this.name}`);
    
    // Schedule the cron job
    this.cronJob = cron.schedule(this.cronExpression, async () => {
      if (this.isRunning) {
        this.logger.info(`Worker ${this.name} is already running, skipping this execution`);
        return;
      }

      try {
        this.isRunning = true;
        this.logger.info(`Running worker: ${this.name}`);
        await this.processBatch();
        this.logger.info(`Worker ${this.name} completed successfully`);
      } catch (error) {
        this.logger.error(`Error in worker ${this.name}:`, error);
      } finally {
        this.isRunning = false;
      }
    });
    
    // Run after initial delay
    setTimeout(() => {
      this.logger.info(`Initial delayed execution of worker ${this.name} after ${this.initialDelay}ms`);
      this.runImmediate();
    }, this.initialDelay);
  }

  /**
   * Run the worker once immediately
   */
  public async runImmediate(): Promise<void> {
    if (this.isRunning) {
      this.logger.info(`Worker ${this.name} is already running, cannot run immediately`);
      return;
    }

    try {
      this.isRunning = true;
      this.logger.info(`Running worker ${this.name} immediately`);
      await this.processBatch();
      this.logger.info(`Immediate run of worker ${this.name} completed successfully`);
    } catch (error) {
      this.logger.error(`Error in immediate run of worker ${this.name}:`, error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Stop the worker
   */
  public stop(): void {
    if (this.cronJob) {
      this.logger.info(`Stopping worker: ${this.name}`);
      this.cronJob.stop();
    }
  }
}
