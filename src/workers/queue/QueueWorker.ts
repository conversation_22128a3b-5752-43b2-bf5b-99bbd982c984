import { EventEmitter } from 'events';
import PgBoss from 'pg-boss';
import { Logging } from '../../util';
import { IQueueHandler } from '../../handlers';
import { container } from '../../configs/container';

// Generic options for any queue worker
export interface QueueWorkerOptions {
  workerId: string;
  queueName: string;
  handlers?: Record<string, IQueueHandler>;
  [key: string]: any;
}

export class QueueWorker extends EventEmitter {
  private boss: PgBoss;
  private logger: Logging;
  private isShuttingDown: boolean = false;
  private options: QueueWorkerOptions;
  private handlers: Record<string, IQueueHandler> = {};

  constructor(
    options: QueueWorkerOptions,
  ) {
    super();
    
    this.logger = new Logging();
    
    // Register handlers
    if (options.handlers) {
      this.handlers = options.handlers;
    }
    this.options = options;
  }

  /**
   * Register a handler for a specific queue
   */
  registerHandler(queueName: string, handler: IQueueHandler): void {
    this.handlers[queueName] = handler;
    this.logger.info(`Registered handler for queue: ${queueName}`);
  }

  /**
   * Initialize the worker and start listening to queues
   */
  async initialize(): Promise<boolean> {
    try {
      this.logger.info(`Initializing worker ${this.options.workerId} for queues: ${this.options.queueName}`);
      
      // Emit initialization status
      this.emit('status', { 
        status: 'initialized',
        workerId: this.options.workerId
      });
      
      // Start listening to queues
      await this.startQueueProcessing();
      
      // Set up shutdown handlers
      this.setupShutdownHandlers();
      
      return true;
    } catch (error) {
      this.logger.error('Failed to initialize worker', error);
      this.emit('error', error);
      throw error;
    }
  }

  private setupShutdownHandlers(): void {
    // Handle process termination signals
    process.on('SIGTERM', async () => await this.shutdown());
    process.on('SIGINT', async () => await this.shutdown());
  }

  /**
   * Gracefully shut down the worker
   */
  async shutdown(): Promise<boolean> {
    if (this.isShuttingDown) return true;
    
    this.isShuttingDown = true;
    this.logger.info(`Worker ${this.options.workerId} shutdown initiated`);
    
    try {
      await this.boss.stop();
      
      this.emit('status', { 
        status: 'shutdown',
        workerId: this.options.workerId 
      });
      
      this.emit('shutdown');
      
      return true;
    } catch (error) {
      this.logger.error('Error during shutdown', error);
      this.emit('error', error);
      throw error;
    }
  }

  private async startQueueProcessing(): Promise<void> {
    const { queueNames } = this.options;
    
    if (queueNames.length === 0) {
      this.logger.warn('No queue names specified for this worker. Worker will be idle.');
      return;
    }
    
    this.logger.info(`Starting processing for queues: ${queueNames.join(', ')}`);
    
    await this.startProcessingQueue()
    
    this.emit('status', { 
      status: 'listening',
      workerId: this.options.workerId,
      queues: queueNames
    });
  }

  private async startProcessingQueue(): Promise<void> {
    const { workerId } = this.options;
    
    // Check if we have a handler for this queue
    const handler = this.handlers[this.options.queueName];
    if (!handler) {
      this.logger.warn(`No handler registered for queue: ${this.options.queueName}, skipping`);
      return;
    }
    
    this.logger.info(`Worker ${workerId} starting to process queue: ${this.options.queueName}`);

    const jobQueueService = container.resolve('jobQueueService');
    
    await jobQueueService.listenToJobQueue(this.options.queueName, async (job) => {
      this.logger.info(`Worker ${workerId} processing item ${job.id} from queue ${this.options.queueName}`);
      
      // Track metrics
      const startTime = Date.now();
      
      try {
        // Process the item using the registered handler
        const result = await handler.handleData(job);
        
        // Report success
        const duration = Date.now() - startTime;
        this.emit('item_processed', { 
          workerId,
          itemId: job.id,
          queueNam: this.options.queueName,
          duration,
          success: result
        });
        
        this.logger.info(`Successfully processed item ${job.id} in ${duration}ms`);
      } catch (error) {
        // Report failure
        this.logger.error(`Failed to process item ${job.id}`, error);
        
        this.emit('item_error', { 
          workerId,
          itemId: job.id,
          queueName: this.options.queueName,
          error: error.message,
          stack: error.stack
        });
        
        // Rethrow to let pg-boss handle retry logic
        throw error;
      }
    });
  }
}