import { Logging } from "../../util";
import { QueueWorker, QueueWorkerOptions } from "./QueueWorker";

// src/worker-manager.ts
export class QueueWorkerManager {
  private workers: Map<string, QueueWorker> = new Map();
  private logger: Logging;
  
  constructor() {
    this.logger = new Logging();
  }
  
  /**
   * Create and start a queue worker
   */
  startWorker(options: QueueWorkerOptions): void {    
    const {workerId, queueName} = options;
    this.logger.info(`Starting worker ${workerId} for queues: ${queueName}`);
    
    // Kill existing worker with this ID if it exists
    if (this.workers.has(workerId)) {
      this.stopWorker(workerId);
    }

    const worker = new QueueWorker(options);
    
    // Set up event handlers
    worker.on('status', (status) => {
      this.logger.info(`Worker ${workerId} status: ${status.status}`);
    });
    
    worker.on('item_processed', (data) => {
      this.logger.info(`Worker ${workerId} processed item ${data.itemId} from ${data.queueName} in ${data.duration}ms`);
    });
    
    worker.on('item_error', (data) => {
      this.logger.error(`Worker ${workerId} failed item ${data.itemId}: ${data.error}`);
    });
    
    worker.on('error', (error) => {
      this.logger.error(`Worker ${workerId} error:`, error);
      // Restart the worker after a delay
      setTimeout(() => this.startWorker(options), 5000);
    });
    
    worker.on('shutdown', () => {
      this.logger.info(`Worker ${workerId} shut down`);
      this.workers.delete(workerId);
    });
    
    // Initialize the worker
    worker.initialize().catch(error => {
      this.logger.error(`Failed to initialize worker ${workerId}:`, error);
    });
    
    // Store the worker reference
    this.workers.set(workerId, worker);
  }
  
  /**
   * Stop a specific worker
   */
  stopWorker(workerId: string): void {
    const worker = this.workers.get(workerId);
    if (worker) {
      this.logger.info(`Stopping worker ${workerId}`);
      worker.shutdown().catch(error => {
        this.logger.error(`Error shutting down worker ${workerId}:`, error);
      });
    }
  }
  
  /**
   * Stop all workers
   */
  async stopAllWorkers(): Promise<void> {
    this.logger.info('Stopping all workers');
    const shutdownPromises = [];
    
    for (const [workerId, worker] of this.workers.entries()) {
      this.logger.info(`Stopping worker ${workerId}`);
      shutdownPromises.push(worker.shutdown());
    }
    
    await Promise.all(shutdownPromises);
  }
  
  /**
   * Get status of all workers
   */
  getWorkerStatus(): { totalWorkers: number; workerIds: string[] } {
    return {
      totalWorkers: this.workers.size,
      workerIds: Array.from(this.workers.keys())
    };
  }
}