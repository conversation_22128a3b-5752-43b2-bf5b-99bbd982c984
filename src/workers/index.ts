import { CronWorkerManager } from './cron/CronWorkerManager';
import { JobCronWorker } from './cron/JobCronWorker';

// const workerManager = new QueueWorkerManager();

// export const setupWorkers = () => {
//   workerManager.startWorker({
//       workerId: 'job-scheduler-worker',
//       queueName: QueueName.JOB_QUEUE,
//       handlers: {
//         'job-scheduler-worker': new JobQueueHandler(),
//       }
//   });
// }


export {CronWorkerManager, JobCronWorker}