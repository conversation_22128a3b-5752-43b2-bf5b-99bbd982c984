// Mock for the database connection
export const mockRepository = {
  findOneOrFail: jest.fn(),
  findOne: jest.fn(),
  find: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getOne: jest.fn(),
    getOneOrFail: jest.fn(),
    getMany: jest.fn(),
    execute: jest.fn(),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    whereInIds: jest.fn().mockReturnThis(),
    softDelete: jest.fn().mockReturnThis(),
    setParameters: jest.fn().mockReturnThis(),
    getQuery: jest.fn().mockReturnValue('mocked-query'),
  })),
  exists: jest.fn(),
  existsBy: jest.fn(),
  findOneBy: jest.fn(),
  findOneByOrFail: jest.fn(),
  count: jest.fn(),
  findAndCount: jest.fn(),
};

// Mock for the AppDatabaseClient
export const AppDatabaseClient = {
  getRepository: jest.fn().mockReturnValue(mockRepository),
  initialize: jest.fn().mockResolvedValue(true),
  isInitialized: true,
};

// Mock for typeorm
export const mockDataSource = {
  initialize: jest.fn().mockResolvedValue(true),
  isInitialized: true,
  getRepository: jest.fn().mockReturnValue(mockRepository),
};
