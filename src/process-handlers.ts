import { Logging } from "./util";
import { CronWorkerManager } from "./workers";


export const setupProcessHandlers = (cronManager: CronWorkerManager, logger: Logging): void => {
  const handleShutdown = async (signal: string) => {
    logger.info(`Received ${signal} signal`);
    await cronManager.stopAll();
    // Add any additional cleanup here
    process.exit(0);
  };

  // Handle normal termination signals
  process.on('SIGTERM', () => handleShutdown('SIGTERM'));
  process.on('SIGINT', () => handleShutdown('SIGINT'));
  
  // Handle nodemon/ts-node-dev restarts
  process.on('SIGUSR2', () => handleShutdown('SIGUSR2'));
  
  // Handle uncaught exceptions and unhandled rejections
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception:', error);
    handleShutdown('uncaughtException');
  });
  
  process.on('unhandledRejection', (reason, promise) => {
    logger.error(`Unhandled rejection at: ${promise} reason: ${reason}`);
    handleShutdown('unhandledRejection');
  });
};