import { CreateJobAssignedEmployeeTable1717675598000 } from './../migrations/1717675598000-CreateJobAssignedEmployeeTable';
import { DataSource } from "typeorm"
import path from "path";
import fs from 'fs';
import "reflect-metadata";
import { AdditionalDetailsChoicesEntity, AdditionalDetailsEntity, AddressProfileEntity, 
    AdminProfileEntity, 
    ClientProfileEntity, CompanyAllowedDomainEntity, CompanyProfileEntity,
    EmployeeProfileEntity, JobAssignedAdditionalDetailsEntity, JobAssignedConfigurationEntity, 
    JobAssignedEmployeeEntity, JobAssignedExtraEntity, JobAssignedPriceEntity, 
    JobEntity, JobExtraEntity, ProfileEntity, 
    RefreshTokenEntity, ScheduledJobDetailsEntity, 
    ServiceAreaEntity, 
    ServiceFrequencyEntity, 
    ServicePropertySetupEntity, 
    TypeOfServiceEntity, UserAuthEntity, UserCompanyAssociationEntity, UserProfilesPerCompany } from "../entity/v1";
import * as dotenv from 'dotenv'
import { AdminProfileSubscriber, EmployeeProfileSubscriber, ClientProfileSubscriber } from "../subscribers";
import { CreateTypeOfServiceTable1736303580116,
     CreateUserCompanyAssociationsTable1705001234567, 
    EnableUuidExtension1736303580115, 
    ServiceArea1736948548630, 
    AdditionalDetails1737250035380, 
    ServicePropertySetup1737858149530,
    JobAssignedConfiguration1738201409672,
    ResidentialCleaningJobs1706817895745,
    CreateServiceFrequencyTable1708176800000,
    CreateJobExtraEntity1615815124000,
    CreateJobAssignedExtraTable1615815125000,
    CreateJobAssignedAdditionalDetailsTable1615815126000,
    CreateJobAssignedPriceTable1710356789000,
    UpdateResidentialCleaningJobTable1711295873254,
    CreateJobInfoTable1712244589733,
    CreateScheduledJobTable1712244689733} from "../migrations";
import { CreateCompanyAllowedDomain1708444800000 } from "../migrations/1708444800000-CreateCompanyAllowedDomain";


dotenv.config()

const configFile = path.join(__dirname, '../../ormconfig.json');
const configContents = fs.readFileSync(configFile, 'utf-8');
const config = JSON.parse(configContents);
const { DB_HOST, DB_PORT, DB_USERNAME, DB_PASSWORD, DB_NAME, NODE_ENV, DB_SCHEMA_NAME } =
  process.env;

export const AppDatabaseClient = new DataSource({
    type: config.type,
    host: DB_HOST ? DB_HOST: config.host,
    port: DB_PORT ? DB_PORT: config.port,
    username: DB_USERNAME,
    password: DB_PASSWORD,
    database: DB_NAME,
    synchronize: false,
    entities: [
        JobEntity, 
        UserAuthEntity,
        CompanyProfileEntity, 
        AddressProfileEntity,
        AdminProfileEntity,
        ProfileEntity,
        EmployeeProfileEntity, 
        ClientProfileEntity,
        ScheduledJobDetailsEntity, 
        JobExtraEntity,
        UserProfilesPerCompany, 
        RefreshTokenEntity, 
        TypeOfServiceEntity, 
        UserCompanyAssociationEntity, 
        ServiceAreaEntity, 
        AdditionalDetailsEntity,
        AdditionalDetailsChoicesEntity,
        ServicePropertySetupEntity,
        JobAssignedConfigurationEntity,
        JobAssignedExtraEntity,
        JobAssignedAdditionalDetailsEntity,
        ServiceFrequencyEntity,
        CompanyAllowedDomainEntity,
        JobAssignedPriceEntity,
        JobAssignedEmployeeEntity
    ],
    migrations: [
        CreateUserCompanyAssociationsTable1705001234567, 
        CreateTypeOfServiceTable1736303580116, 
        EnableUuidExtension1736303580115, 
        ServiceArea1736948548630,
        AdditionalDetails1737250035380,
        ServicePropertySetup1737858149530,
        JobAssignedConfiguration1738201409672,
        ResidentialCleaningJobs1706817895745,
        CreateServiceFrequencyTable1708176800000,
        CreateCompanyAllowedDomain1708444800000,
        CreateJobExtraEntity1615815124000,
        CreateJobAssignedExtraTable1615815125000,
        CreateJobAssignedAdditionalDetailsTable1615815126000,
        CreateJobAssignedPriceTable1710356789000,
        UpdateResidentialCleaningJobTable1711295873254,
        CreateJobInfoTable1712244589733,
        CreateScheduledJobTable1712244689733,
        CreateJobAssignedEmployeeTable1717675598000
        // ResidentialJobPerEmployeeTable1706817895746
    ],
    subscribers: [
        AdminProfileSubscriber,
        EmployeeProfileSubscriber,
        ClientProfileSubscriber
    ],
    schema: DB_SCHEMA_NAME || 'public',
    logging:true,
    poolSize: 10,
    maxQueryExecutionTime: 1000
})

AppDatabaseClient.initialize()
    .then(() => {
        console.log("Data Source has been initialized!")
    })
    .catch((err) => {
        console.error("Error during Data Source initialization", err)
    })
