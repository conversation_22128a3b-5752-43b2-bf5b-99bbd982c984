import { Request, Response } from 'express';
import * as crypto from 'crypto';
import { HTTP_ERROR_TYPES } from '../constants';
import { BaseError } from '../errors';

type CsrfConfig = {
  secret: string,
  cookieName: string
  cookieOptions: {
    httpOnly: boolean,
    path: string,
    maxAge: number,
    sameSite: 'lax' | 'strict' | undefined,
    secure: boolean
  }
}

const csrfConfig: CsrfConfig = {
  secret: process.env.CSRF_TOKEN_SECRET,
  cookieName: 'x-csrf-token',
  cookieOptions: {
    maxAge: 86400,
    httpOnly: process.env.NODE_ENV === 'development' ? false : true,
    sameSite: process.env.NODE_ENV === 'development' ? 'lax' as const : 'strict' as const,
    secure: process.env.NODE_ENV === 'development' ? false : true,
    path: '/'
  }
}

const createSignature = (payload: string): string => {
  const hmac = crypto.createHmac('sha256', csrfConfig.secret);
  hmac.update(payload);
  return hmac.digest('hex');
}

const generateCsrfToken = (res: Response): void => {
  const randomToken = crypto.randomBytes(32).toString('hex');
    
  // Current timestamp for expiration
  const timestamp = Date.now();
  
  // Create payload
  const payload = `${randomToken}|${timestamp}`;
  
  // Sign the payload
  const signature = createSignature(payload);
  
  // Combine into final token
  const token = `${signature}|${payload}`;
  
  // Set as cookie
  res.cookie(csrfConfig.cookieName, token, {
    httpOnly: csrfConfig.cookieOptions.httpOnly,
    sameSite: csrfConfig.cookieOptions.sameSite,
    secure: csrfConfig.cookieOptions.secure,
    maxAge: csrfConfig.cookieOptions.maxAge,
    path: csrfConfig.cookieOptions.path
  });
}

const validateCsrfToken = (req: Request): {refresh: boolean} => {
  try {
    const cookieToken = req.cookies[csrfConfig.cookieName];
    if (!cookieToken) {
      throw new BaseError(HTTP_ERROR_TYPES.UNKNOWN_AUTHENTICATION_ERROR, `Invalid CSRF not present.`)
    }
    
    // Parse token parts
    const parts = cookieToken.split('|');
    if (parts.length !== 3) {
      throw new BaseError(HTTP_ERROR_TYPES.UNKNOWN_AUTHENTICATION_ERROR, `Invalid CSRF invalid.`)

    }
    
    const [signature, token, timestampStr] = parts;
    const timestamp = parseInt(timestampStr, 10);
    
    // Check token expiration
    if (Date.now() - timestamp > csrfConfig.cookieOptions.maxAge) {
      return {
        refresh: true,
      };
    }
    
    // Verify signature
    const payload = `${token}|${timestampStr}`;
    const expectedSignature = createSignature(payload);
    
    if(signature !== expectedSignature){
      throw new BaseError(HTTP_ERROR_TYPES.UNKNOWN_AUTHENTICATION_ERROR, `Invalid CSRF token.`)
    }
    return {
      refresh: false,
    };
  } catch (error) {
    throw new BaseError(HTTP_ERROR_TYPES.UNKNOWN_AUTHENTICATION_ERROR, `Cannot validate your CSRF token.`)
  }
}

export {generateCsrfToken, validateCsrfToken};