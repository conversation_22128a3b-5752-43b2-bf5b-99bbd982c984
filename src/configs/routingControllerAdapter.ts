import { container } from "./container";

// Create a debug wrapper that logs resolution attempts
export const containerAdapter = {
  get(someClass) {
    const className = someClass.name;
    console.log(`Resolving dependencies for: ${className}`);
    
    try {
      // Try to instantiate with all dependencies from container
      const instance = container.cradle[className.charAt(0).toLowerCase() + className.slice(1)];
      if (instance) return instance;
      
      // If not registered as a whole, try to resolve constructor parameters
      return new someClass(...getParamNamesForClass(someClass).map(paramName => {
        console.log(`Resolving parameter: ${paramName}`);
        return container.resolve(paramName);
      }));
    } catch (e) {
      console.warn(`Error resolving ${className}:`, e.message);
      return new someClass(); // Fallback
    }
  }
};

// Helper to get parameter names (simplified)
function getParamNamesForClass(cls) {
  // This is a simplified example - in practice you may need 
  // a more robust way to extract parameter names
  const str = cls.toString();
  const match = str.match(/constructor\s*\(\s*([^)]*)\)/);
  if (!match) return [];
  
  return match[1].split(',').map(param => 
    param.trim().replace(/private|readonly|public|protected|\s|:[^,]+/g, '')
  );
}