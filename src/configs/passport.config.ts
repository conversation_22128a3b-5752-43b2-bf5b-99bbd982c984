import passportJwt, { StrategyOptions } from 'passport-jwt';
import passport from 'passport';
import dotenv from 'dotenv';
dotenv.config();

const JwtStrategy = passportJwt.Strategy
const ExtractJwt = passportJwt.ExtractJwt

const jwtStrategyOptions: StrategyOptions = 
{
    jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
    secretOrKey: process.env.JWT_SECRET,
    algorithms: ['HS256']
}

passport.use(
	new JwtStrategy(
        jwtStrategyOptions,
		async (payload, done) => {
			try {
				done(null, payload)
			} catch (error) {
				console.log(error)
				done(error, false);
			}
		}
	)
);

export default passport;