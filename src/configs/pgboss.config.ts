// src/config/database.ts
import PgBoss from 'pg-boss';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export interface PgBossConfig {
  host?: string;
  port?: number;
  database?: string;
  user?: string;
  password?: string;
  connectionString?: string;
}

export const createPgBossInstance = (config?: PgBossConfig): PgBoss => {
  // Priority: 
  // 1. Provided config object
  // 2. Environment variables
  // 3. Default connection string
  const pgBossConfig: PgBoss.DatabaseOptions = {};

  if (config?.connectionString) {
    pgBossConfig.connectionString = config.connectionString;
  } else {
    pgBossConfig.host = config?.host || process.env.DB_HOST || 'localhost';
    pgBossConfig.port = config?.port || parseInt(process.env.DB_PORT || '5432');
    pgBossConfig.database = config?.database || process.env.DB_NAME || 'jobschedulerdb';
    pgBossConfig.user = config?.user || process.env.DB_USER || 'postgres';
    pgBossConfig.password = config?.password || process.env.DB_PASSWORD || '';
  }
  
  const boss = new PgBoss(pgBossConfig);

  boss.on('error', (error) => {
    console.error('PgBoss error:', error);
  });

  return boss;
};
