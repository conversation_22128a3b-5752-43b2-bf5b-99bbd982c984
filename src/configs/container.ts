import { JobService } from './../services/jobs/JobService';
import { asClass, createContainer, InjectionMode} from "awilix";
import { JobController } from "../controllers";
import { PriceRepository } from "../db/jobs/PriceRepository";
import { PriceService } from "../services/jobs/PriceService";
import { ErrorHandlerMiddleware } from "../middlewares";
import { QueueService } from '../services';
import { JobRepository } from '../db';
import { Logging } from '../util';

// export class RoutingControllersAdapter {
//     constructor(private container: AwilixContainer) {}
  
//     get<T>(someClass: { new (...args: any[]): T }): T {
//         // Convert class constructor to a string that Awilix can use
//         const name = someClass.name.charAt(0).toLowerCase() + someClass.name.slice(1);
//         // // Try to get from request scope first (if available)
//         // const httpContext = require('express-http-context'); // or similar library
//         // const requestContainer = httpContext.get('container');
        
//         // if (requestContainer) {
//         //     return requestContainer.resolve(name);
//         // }
//         return this.container.resolve(name);
//       }
//   }

// export function setupContainer(){
const container = createContainer({
    injectionMode: InjectionMode.CLASSIC
})

// Register repositories
container.register({
    jobRepository: asClass(JobRepository).singleton(),
    priceRepository : asClass(PriceRepository).singleton(),
})

// Register services
container.register({
    priceService: asClass(PriceService).singleton(),
    jobService: asClass(JobService).singleton(),
    jobQueueService: asClass(QueueService).singleton()
})

// Register controllers
container.register({
    jobController: asClass(JobController).singleton()
})

// Register middleware
container.register({
    errorHandlerMiddleware: asClass(ErrorHandlerMiddleware).singleton(),
    logger: asClass(Logging).singleton(),
})

export {container};
// }

// export function configureContainerWithRoutingControllers() {
//     const container = setupContainer();
//     return setupContainer();
// }
