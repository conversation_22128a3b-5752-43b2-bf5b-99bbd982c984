import Logging from "./logger";

/**
 * Simplified approach to analyze performance without parameter conversion complexity
 */
export const analyzeQueryPerformance = async (jobId: string, repository: any): Promise<void> => {
    const logger = new Logging();
    if(logger.isDevEnabled()){
        try {
            // Get entity manager from the repository
            const entityManager = repository.manager;
            
            // Use a simpler direct query for analysis that matches your schema
            const explainSql = `
                EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) 
                SELECT * FROM midori.scheduled_job sj
                WHERE sj.id = $1
            `;
            
            // Run the query with jobId as a direct parameter
            const result = await entityManager.query(explainSql, [jobId]);
            
            // Process the result
            if (result && result[0] && Array.isArray(result[0]["QUERY PLAN"]) && result[0]["QUERY PLAN"] && result[0]["QUERY PLAN"][0].Plan) {
                const plan = result[0]["QUERY PLAN"][0];
                
                logger.info(`Query plan summary for job ${jobId}:`);
                logger.info(`- Execution Time: ${plan['Execution Time']} ms`);
                logger.info(`- Planning Time: ${plan['Planning Time']} ms`);
                
                // Check for index usage on the primary lookup
                if (hasSequentialScan(plan.Plan, 'scheduled_job')) {
                    logger.warn(`Sequential scan detected on scheduled_job table - consider reviewing indexes`);
                } else {
                    logger.info(`Using index for primary job lookup (good)`);
                }
            } else {
                logger.info(`Could not parse EXPLAIN ANALYZE result for job ${jobId}`);
            }
            
            // More detailed analysis of the full join query would be too complex
            // This simplified approach gives basic performance insights without the parameter complexity
        } catch (error) {
            logger.error(`Error analyzing query performance: ${error.message}`);
        }
    }
}

/**
 * Checks if a specific table has a sequential scan in the plan
 */
const hasSequentialScan = (planNode: any, tableName: string): boolean => {
    if (planNode['Node Type'] === 'Seq Scan' && 
        planNode['Relation Name'] && 
        planNode['Relation Name'].includes(tableName)) {
        return true;
    }
    
    if (planNode['Plans'] && Array.isArray(planNode['Plans'])) {
        for (const childPlan of planNode['Plans']) {
            if (hasSequentialScan(childPlan, tableName)) {
                return true;
            }
        }
    }
    
    return false;
}