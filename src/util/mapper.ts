import { ProfileUserTypes } from "../constants";
import { AdminProfileDto, ClientProfileDto, EmployeeProfileDto } from "../dto/v1";
import { AddressInfoDto } from "../dto/v1/profile/ProfileDto";
import { ClientProfileEntity, CompanyProfileEntity, EmployeeProfileEntity, UserAuthEntity } from "../entity/v1";
import AdminProfileEntity from "../entity/v1/profile/AdminProfileEntity";
import { UserRequestObj } from "../types";

export const mapClientProfileDtoToProfileEntity = (user: UserRequestObj, dto: ClientProfileDto): ClientProfileEntity => {
    const profileEntity: ClientProfileEntity = new ClientProfileEntity();
    profileEntity.profile_id = dto.profileId; 
    profileEntity.first_name = dto.firstName;
    profileEntity.last_name = dto.lastName;
    profileEntity.phone_number = dto.phoneNumber;
    profileEntity.email = dto.email;
    profileEntity.company_name = dto.companyName;
    profileEntity.related_company_id = user.companyId;
    profileEntity.street = dto.addressInfo?.street;
    profileEntity.city = dto.addressInfo?.city;
    profileEntity.state = dto.addressInfo?.state;
    profileEntity.postal_code = dto.addressInfo?.postalCode;
    
    return profileEntity;
}

export const mapClientProfileEntityToDto = (entity: ClientProfileEntity ): ClientProfileDto => {
    return {
        profileId: entity.profile_id,
        firstName: entity.first_name,
        lastName: entity.last_name,
        phoneNumber: entity.phone_number,
        email: entity.email,
        companyName: entity.company_name,
        addressInfo: mapEntityToAddressDto(entity),
    };
}

export const mapAdminProfileDtoToProfileEntity = (user: UserRequestObj, dto: AdminProfileDto): AdminProfileEntity => {
    const entity: AdminProfileEntity = new AdminProfileEntity();
    entity.profile_id = dto.profileId;
    entity.first_name = dto.firstName;
    entity.last_name = dto.lastName;
    entity.phone_number= dto.phoneNumber;
    entity.email= dto.email;
    entity.related_user_id = user?.userId;

    const companyEntity: CompanyProfileEntity = new CompanyProfileEntity();
    companyEntity.company_id = dto.companyInfo?.companyId;
    companyEntity.company_name = dto.companyInfo?.companyName;
    companyEntity.company_website = dto.companyInfo?.companyWebsite;
    companyEntity.company_phone = dto.companyInfo?.companyPhone;
    companyEntity.company_email = dto.companyInfo?.companyEmail;
    companyEntity.street = dto.companyInfo?.addressInfo?.street;
    companyEntity.city = dto.companyInfo?.addressInfo?.city;
    companyEntity.state = dto.companyInfo?.addressInfo?.state;
    companyEntity.postal_code = dto.companyInfo?.addressInfo?.postalCode;

    entity.company_info = companyEntity;

    return entity;
}

export const mapAdminProfileEntityToDto = (entity: AdminProfileEntity): AdminProfileDto => {
    return {
        profileId: entity.profile_id,
        firstName: entity.first_name,
        lastName: entity.last_name,
        phoneNumber: entity.phone_number,
        email: entity.email,
        companyInfo: {
            companyId: entity.company_info.company_id,
            companyName: entity.company_info.company_name,
            companyWebsite: entity.company_info.company_website,
            companyPhone: entity.company_info.company_phone,
            companyEmail: entity.company_info.company_email,
            addressInfo: mapEntityToAddressDto(entity.company_info),
        },
        position: entity.profile_type as ProfileUserTypes
    };
}

export const mapEmployeeProfileDtoToProfileEntity = (user: UserRequestObj, dto: EmployeeProfileDto): EmployeeProfileEntity => {
    const profileEntity: EmployeeProfileEntity = new EmployeeProfileEntity();
    profileEntity.profile_id = dto.profileId;
    profileEntity.first_name = dto.firstName;
    profileEntity.last_name = dto.lastName;
    profileEntity.date_of_birth = dto.dateOfBirth;
    profileEntity.phone_number = dto.phoneNumber;
    profileEntity.email = dto.email;
    profileEntity.street = dto.addressInfo?.street;
    profileEntity.city = dto.addressInfo?.city;
    profileEntity.state = dto.addressInfo?.state;
    profileEntity.postal_code = dto.addressInfo?.postalCode;
    profileEntity.position = dto.position;
    profileEntity.related_company_id = user?.companyId;

    return profileEntity;
}

export const mapEmployeeEntityToEmployeeProfileDto = (entity: EmployeeProfileEntity): EmployeeProfileDto  => {
    return {
        profileId: entity?.profile_id,
        firstName: entity?.first_name,
        lastName: entity?.last_name,
        phoneNumber: entity?.phone_number,
        email: entity?.email,
        addressInfo: mapEntityToAddressDto(entity),
        dateOfBirth: entity?.date_of_birth,
        position: entity?.position,
    };
}

const mapEntityToAddressDto = (entity: any): AddressInfoDto => {
    return {
        street: entity?.street,
        city: entity?.city,
        state: entity?.street,
        postalCode: entity?.postal_code,
    }
}
