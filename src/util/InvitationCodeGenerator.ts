import crypto from 'crypto';
import Logging from './logger';
import { AuthenticationError } from '../errors';
import { HTTP_ERROR_TYPES } from '../constants';

export default class InvitationCodeGenerator {
    static logger: Logging = new Logging();

    static generateInvitationCode = (role: string) => {
        const expirationDate = Math.floor(Date.now() / 1000) + 24 * 60 * 60;
        const data = `${role}:${expirationDate}`;
        const key = Buffer.from(process.env.INVITATION_SECRET_KEY || '', 'hex');
        const iv =  Buffer.from(process.env.INVITATION_CODE_IV || '', 'hex');
        const cipher = crypto.createCipheriv('aes-128-cbc', key, iv);
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        // const authTag = cipher.getAuthTag().toString('hex');
        const invitationCode = `${encrypted}`;

        this.logger.debug(`[Invitation Code Encryption] Code with role ${role} created.`);
        return {
            invitation_code: invitationCode
        }

    };

    static verifyInvitationCode = (encryptedCode: string) => {
        try {

            const key = Buffer.from(process.env.INVITATION_SECRET_KEY || '', 'hex');
            const iv =  Buffer.from(process.env.INVITATION_CODE_IV || '', 'hex');
            // const [authTagHex, encryptedData] = encryptedCode.split('-');
            // const authTag = Buffer.from(authTagHex, 'hex');
            const decipher = crypto.createDecipheriv('aes-128-cbc', key, iv);
            // decipher.setAuthTag(authTag);

            let decrypted = decipher.update(encryptedCode, 'hex', 'utf8');
            decrypted += decipher.final('utf8');

            const [role, expirationDate] = decrypted.split(':');
            const currentTime = Math.floor(Date.now() / 1000);

            // Check if the code is expired
            if (currentTime > parseInt(expirationDate, 10)) {
                this.logger.error(`[Invitation Code Decryption] Code: ${encryptedCode} is expired.`);
                throw new AuthenticationError({
                    name: HTTP_ERROR_TYPES.INVITATION_CODE_VALIDATION_ERROR,
                    message: 'Invitation code is expired.'
                });
            }

            if (role) {
                return role;
            } else {
                this.logger.error(`[Invitation Code Decryption] Code: ${encryptedCode} does not contain a role.`);
                throw new AuthenticationError({
                    name: HTTP_ERROR_TYPES.INVITATION_CODE_VALIDATION_ERROR,
                    message: 'Invitation code is malformed.'
                })
            }
        } catch (error) {
            this.logger.error(`[Invitation Code Decryption] Error: ${error.message}`);
            throw new AuthenticationError({
                name: HTTP_ERROR_TYPES.INVITATION_CODE_VALIDATION_ERROR,
                message: 'Invitation code is malformed.'
            })
        }
    };
}
