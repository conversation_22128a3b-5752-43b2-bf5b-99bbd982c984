import * as crypto from 'crypto';
import { UserAuthEntity } from "../entity/v1";
import jwt, { Secret, SignOptions } from 'jsonwebtoken';

export const generateTokens = async (user: UserAuthEntity, companyId?: number) => {
    const accessToken: string = generateAccessToken(user, companyId);
    const refreshToken: string = generateRefreshToken(user);

    return {accessToken,refreshToken};
}

export const generateAccessToken = (user: UserAuthEntity, companyId?: number) => {
    const payload = { 
        userId: user.user_id as number,
        companyId: companyId as number,
        role: user.role as string
    };
    
    const options: SignOptions = {
        expiresIn: Number(process.env.ACCESS_TOKEN_EXPIRY),
        algorithm: "HS256"
    };

    return jwt.sign(
        payload,
        process.env.JWT_SECRET as Secret,
        options
    );
}

export const generateRefreshToken = (user: UserAuthEntity) => {
    const payload = { 
        userId: user.user_id as number,
    };

    const options: SignOptions = {
        expiresIn: Number(process.env.REFRESH_TOKEN_EXPIRY),
        algorithm: "HS256"
    };

    return jwt.sign(
        payload,
        process.env.REFRESH_JWT_SECRET as Secret,
        options
    );
}

export const generatePartnerToken = (companyId: number) => {
    const expireTime = Date.now() + (3600 * 1000);


    const payload = { 
        companyId: companyId as number,
    };

    const options: SignOptions = {
        expiresIn: 14400,
        algorithm: "HS256"
    };

    return jwt.sign(
        payload,
        process.env.JWT_SECRET as Secret,
        options
    );
}

export const verifyRefreshToken = (token: string): any => {

    if(!process.env.REFRESH_JWT_SECRET){
        throw new Error('JWT_KEY must be defined')
    }

    return jwt.verify(token, process.env.REFRESH_JWT_SECRET);
}
