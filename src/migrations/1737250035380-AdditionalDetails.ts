import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class AdditionalDetails1737250035380 implements MigrationInterface {
    name = 'AdditionalDetails1737250035380'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ADDITIONAL_DETAILS_CHOICES}
             ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
             "name" text NOT NULL, 
             "price" numeric(10,2) NOT NULL, 
             "currency" text NOT NULL, 
             "related_additional_detail_id" uuid NOT NULL, 
             CONSTRAINT "PK_40b1685b7d5008cc0da12f096be" PRIMARY KEY ("id"))`);
        await queryRunner.query(
            `CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ADDITIONAL_DETAILS}
            ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
            "name" text NOT NULL, 
            "description" text NOT NULL, 
            "type_of_cleaning" text NOT NULL, 
            "is_active" boolean NOT NULL, 
            "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "related_company_id" integer NOT NULL, 
            CONSTRAINT "PK_37c36ac42572cf6b3adaa19fdf0" PRIMARY KEY ("id"))`);
        await queryRunner.query(
            `ALTER TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ADDITIONAL_DETAILS_CHOICES} 
            ADD CONSTRAINT "FK_8e7ca27e6eb65a3db97bde77020"
            FOREIGN KEY ("related_additional_detail_id") 
            REFERENCES ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ADDITIONAL_DETAILS}("id") 
            ON DELETE CASCADE ON UPDATE CASCADE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ADDITIONAL_DETAILS_CHOICES}  DROP CONSTRAINT "FK_8e7ca27e6eb65a3db97bde77020"`);
        await queryRunner.query(`DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ADDITIONAL_DETAILS}`);
        await queryRunner.query(`DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ADDITIONAL_DETAILS_CHOICES}`);
    }

}
