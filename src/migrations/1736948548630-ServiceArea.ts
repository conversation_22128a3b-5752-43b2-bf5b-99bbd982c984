import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";
import { JobType, CurrencyType } from "../types";

export class ServiceArea1736948548630 implements MigrationInterface {
    name = 'ServiceArea1736948548630'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create the table with raw SQL
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_SERVICE_AREA} (
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                sq_ft_from integer NOT NULL,
                sq_ft_to integer NOT NULL,
                type_of_cleaning text NOT NULL,
                price DECIMAL(10, 2),
                currency text NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_datetime TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_datetime TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                related_company_id INT NOT NULL,
                CONSTRAINT "PK_5f304a28e7b08983dce479ea598" PRIMARY KEY ("id"),
                CONSTRAINT check_sq_ft_range CHECK (sq_ft_start < sq_ft_end)
            );
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the table with raw SQL
        await queryRunner.query(`
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_SERVICE_AREA};
        `);
    }
}
