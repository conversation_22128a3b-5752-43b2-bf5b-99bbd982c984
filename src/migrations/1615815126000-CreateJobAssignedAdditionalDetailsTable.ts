import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";


export class CreateJobAssignedAdditionalDetailsTable1615815126000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create the table
        await queryRunner.query(`
            CREATE TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_ADDITIONAL_DETAILS} (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                job_id UUID,
                related_additional_details_id UUID NOT NULL,
                related_additional_details_choice_id UUID NOT NULL,
                created_datetime TIMESTAMP NOT NULL DEFAULT now(),
                updated_datetime TIMESTAMP NOT NULL DEFAULT now(),
                
                CONSTRAINT fk_job
                    FOREIGN KEY (job_id)
                    REFERENCES ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE}(id)
                    ON DELETE CASCADE
                    ON UPDATE CASCADE,
                
                CONSTRAINT fk_additional_details
                    FOREIGN KEY (related_additional_details_id)
                    REFERENCES ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ADDITIONAL_DETAILS}(id),
                    
                CONSTRAINT fk_additional_details_choice
                    FOREIGN KEY (related_additional_details_choice_id)
                    REFERENCES ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ADDITIONAL_DETAILS_CHOICES}(id)
            );
        `);

        // Add unique index on related_additional_details_id
        await queryRunner.query(`
            CREATE UNIQUE INDEX idx_job_assigned_additional_details_unique
            ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_ADDITIONAL_DETAILS} (related_additional_details_id);
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.idx_job_assigned_additional_details_unique
        `);
        // Drop the table
        await queryRunner.query(`
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_ADDITIONAL_DETAILS};
        `);
    }
}