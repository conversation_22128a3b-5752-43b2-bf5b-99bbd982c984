import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class CreateJobExtraEntity1615815124000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create the table
        await queryRunner.query(`
            CREATE TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_JOB_EXTRA} (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                name VARCHAR NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                currency VARCHAR,
                description VARCHAR,
                type_of_cleaning VARCHAR NOT NULL,
                is_active BOOLEAN DEFAULT false,
                available_amount INTEGER NOT NULL,
                related_company_id INTEGER NOT NULL,
                created_datetime TIMESTAMP NOT NULL DEFAULT now(),
                updated_datetime TIMESTAMP NOT NULL DEFAULT now(),
                
                -- Add constraint to ensure currency is present when price is present
                CONSTRAINT check_price_currency CHECK (
                    (price IS NULL) OR 
                    (price IS NOT NULL AND currency IS NOT NULL)
                )
            );
        `);

        await queryRunner.query(`
            CREATE INDEX idx_job_extra_company_active 
            ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_JOB_EXTRA}(type_of_cleaning, related_company_id, is_active)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`
            DROP INDEX IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.idx_job_extra_company_active
        `);
        // Drop the table
        await queryRunner.query(`
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_JOB_EXTRA};
        `);
    }
}