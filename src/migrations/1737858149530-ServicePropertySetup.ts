import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class ServicePropertySetup1737858149530 implements MigrationInterface {
    name = 'ServicePropertySetup1737858149530'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_SERVICE_PROPERTY_SETUP}
            ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
            "name" character varying NOT NULL, 
            "description" character varying, 
            "available_amount" integer NOT NULL, 
            "type_of_cleaning" character varying NOT NULL,
            "price" integer, 
            "currency" character varying, 
            "is_active" boolean NOT NULL, 
            "related_company_id" integer NOT NULL, 
            "created_datetime" TIMESTAMP NOT NULL DEFAULT now(), 
            "updated_datetime" TIMESTAMP NOT NULL DEFAULT now(), 
            CONSTRAINT "PK_90ab072bcf09c8965499175370c" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_SERVICE_PROPERTY_SETUP}`);
    }

}
