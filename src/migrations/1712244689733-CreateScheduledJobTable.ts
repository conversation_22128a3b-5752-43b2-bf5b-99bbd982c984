import { MigrationInterface, QueryRunner } from 'typeorm';
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from '../constants/db_tables';


export class CreateScheduledJobTable1712244689733 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        
        // Create scheduled job table
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.SCHEDULED_JOB} (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                start_time TIMESTAMPTZ NOT NULL,
                end_time TIMESTAMPTZ NOT NULL,
                status VARCHAR NOT NULL,
                related_job_id UUID,
                related_service_area_id UUID,
                related_service_type_id UUID,
                related_company_id BIGINT,
                total_price DECIMAL(10, 2),
                createdAt TIMESTAMPTZ DEFAULT NOW(),
                updatedAt TIMESTAMPTZ DEFAULT NOW(),
                deletedAt TIMESTAMPTZ NULL,
                CONSTRAINT fk_job
                    FOREIGN KEY (related_job_id)
                    REFERENCES ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_INFO}(id)
                    ON DELETE CASCADE
                    ON UPDATE CASCADE
            )
        `);

        // Create join table for many-to-many relationship with employees
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_JOB_PER_EMPLOYEE} (
                related_job_id UUID NOT NULL,
                related_employee_profile_id INTEGER NOT NULL,
                PRIMARY KEY (related_job_id, related_employee_profile_id)
            )
        `);

        // Create indexes for faster lookups
        await queryRunner.query(`
            CREATE INDEX idx_scheduled_job_related_job_id 
            ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.SCHEDULED_JOB}(related_job_id);
        `);

        await queryRunner.query(`
            CREATE UNIQUE INDEX idx_unique_job_occurrence 
            ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.SCHEDULED_JOB}(related_job_id, start_time, end_time);
        `);

        // Add an index for performance
        await queryRunner.query(`
            CREATE INDEX idx_scheduled_job_company_id 
            ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.SCHEDULED_JOB}(related_company_id)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the join table first (to resolve foreign key constraints)
        await queryRunner.query(`
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_JOB_PER_EMPLOYEE}
        `);
        
        // Drop indexes
        await queryRunner.query(`
            DROP INDEX IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.idx_scheduled_job_related_job_id
            DROP INDEX IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.idx_unique_job_occurrence
            DROP INDEX IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.idx_scheduled_job_company_id
        `);
        
        // Drop the scheduled job table
        await queryRunner.query(`
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.SCHEDULED_JOB}
        `);
    }
}