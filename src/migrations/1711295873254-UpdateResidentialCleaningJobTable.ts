import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class UpdateResidentialCleaningJobTable1711295873254 implements MigrationInterface {
    
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add new status options if needed
        await queryRunner.query(`
            ALTER TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE} 
            ADD COLUMN IF NOT EXISTS "related_service_frequency_id" uuid
        `);

        await queryRunner.query(`
            ALTER TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE}  
            ADD CONSTRAINT "FK_job_entity_service_frequency" 
            FOREIGN KEY ("related_service_frequency_id") 
            REFERENCES ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_SERVICE_FREQUENCY} ("id") 
            ON DELETE SET NULL
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraint
        await queryRunner.query(`
            ALTER TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE} 
            DROP CONSTRAINT IF EXISTS "FK_job_entity_service_frequency"
        `);
        
        // Drop column
        await queryRunner.query(`
            ALTER TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE} 
            DROP COLUMN IF EXISTS "related_service_frequency_id"
        `);
    }
}