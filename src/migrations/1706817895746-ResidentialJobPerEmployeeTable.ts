import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class ResidentialJobPerEmployeeTable1706817895746 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_JOB_PER_EMPLOYEE}  (
                "related_job_id" UUID NOT NULL,
                "related_employee_profile_id" INTEGER NOT NULL,
                CONSTRAINT "pk_job_per_employee" 
                    PRIMARY KEY ("related_job_id", "related_employee_profile_id"),
                CONSTRAINT "fk_job_per_employee_job" 
                    FOREIGN KEY ("related_job_id") 
                    REFERENCES ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE}("id") 
                    ON DELETE CASCADE 
                    ON UPDATE NO ACTION,
                CONSTRAINT "fk_job_per_employee_employee" 
                    FOREIGN KEY ("related_employee_profile_id") 
                    REFERENCES "employee_profiles"("profile_id") 
            );

            -- Create indexes for better query performance
            CREATE INDEX IF NOT EXISTS "idx_job_per_employee_job_id" 
                ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_JOB_PER_EMPLOYEE} ("related_job_id");
            CREATE INDEX IF NOT EXISTS "idx_job_per_employee_employee_id" 
                ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_JOB_PER_EMPLOYEE} ("related_employee_profile_id");

        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            -- Drop indexes
            DROP INDEX IF EXISTS "idx_job_per_employee_job_id";
            DROP INDEX IF EXISTS "idx_job_per_employee_employee_id";
            DROP INDEX IF EXISTS "uq_job_per_employee_assignment";

            -- Drop the table
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_JOB_PER_EMPLOYEE} ;
        `);
    }
}