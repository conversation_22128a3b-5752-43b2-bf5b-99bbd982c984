import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class CreateCompanySettingsTable1748306369532 implements MigrationInterface {
    name = 'CreateCompanySettingsTable1748306369532'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create company settings table
        await queryRunner.query(
            `CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_DETAILS_SETTINGS} (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "related_company_id" integer NOT NULL, 
                "country_tax" numeric(100,2) NOT NULL, 
                "state_tax" numeric(100,2) NOT NULL, 
                "is_active" boolean NOT NULL DEFAULT true, 
                "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_company_details_settings" PRIMARY KEY ("id")
            )`
        );

        // Create working hours table (fixed the syntax error - double comma)
        await queryRunner.query(
            `CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_WORKING_HOURS_SETTINGS} (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "related_company_settings_id" uuid NOT NULL, 
                "day_of_week" text NOT NULL, 
                "start_time" TIMESTAMPTZ NOT NULL, 
                "end_time" TIMESTAMPTZ NOT NULL, 
                "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_company_working_hours_settings" PRIMARY KEY ("id")
            )`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_DETAILS_SETTINGS} `);
        await queryRunner.query(`DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_WORKING_HOURS_SETTINGS}`);
    }
}
