import { MigrationInterface, QueryRunner } from 'typeorm';

export class EnableUuidExtension1736303580115 implements MigrationInterface {
    name = 'EnableUuidExtension1736303580115';

    async up(queryRunner: QueryRunner): Promise<void> {
        // Create the uuid-ossp extension if it doesn't exist
        await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);
    }

    async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the uuid-ossp extension if it exists
        await queryRunner.query(`DROP EXTENSION IF EXISTS "uuid-ossp"`);
    }
}