import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserCompanyAssociationsTable1705001234567 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
          CREATE SCHEMA IF NOT EXISTS midori;
    
          CREATE TABLE IF NOT EXISTS midori.user_company_associations (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id INT NOT NULL,
            company_id INT NOT NULL,
            profile_id INT NOT NULL,
            role text NOT NULL,
            created_datetime TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_datetime TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
    
          CREATE INDEX IF NOT EXISTS user_company_associations_user_id_company_id_idx ON midori.user_company_associations(user_id, company_id);
          CREATE INDEX IF NOT EXISTS user_company_associations_profile_id_role_idx ON midori.user_company_associations(profile_id, role);
        `);
      }
    
      public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
          DROP TABLE IF EXISTS midori.user_company_associations;
        `);
      }
}