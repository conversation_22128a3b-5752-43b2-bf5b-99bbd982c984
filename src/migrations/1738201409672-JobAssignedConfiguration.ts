import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class JobAssignedConfiguration1738201409672 implements MigrationInterface {
    name = 'JobAssignedConfiguration1738201409672'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_CONFIGURATION} 
            (id uuid NOT NULL DEFAULT uuid_generate_v4(),
            "job_id" uuid, 
            "related_property_setup_id" uuid, 
            "related_additional_details_choice_id" uuid, 
            "amount" integer, 
            "created_datetime" TIMESTAMP NOT NULL DEFAULT now(), 
            "updated_datetime" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT fk_job_config_job 
                FOREIGN KEY (job_id) 
                REFERENCES ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE}(id) 
                ON DELETE CASCADE 
                ON UPDATE CASCADE
            );
               
            CREATE INDEX IF NOT EXISTS idx_job_config_job_id ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_CONFIGURATION}(job_id);`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            -- Drop indexes
            DROP INDEX IF EXISTS "idx_job_config_job_id ";

            -- Drop tables
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_CONFIGURATION} `);
    }
}
