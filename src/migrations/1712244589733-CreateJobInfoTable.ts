import { MigrationInterface, QueryRunner } from 'typeorm';
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from '../constants/db_tables';

export class CreateJobInfoTable1712244589733 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create job table
        await queryRunner.query(`
            CREATE TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_INFO} (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                type VARCHAR NOT NULL,
                related_service_frequency_id UUID NOT NULL,
                related_client_id INTEGER NOT NULL,
                related_company_id INTEGER NOT NULL,
                last_job_generated_datetimeTIMESTAMP WITHOUT TIME ZONE NULL,
                is_active BOOLEAN,
                created_datetime TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
                updated_datetime TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL
            )
        `);

        // Create index for faster lookup
        await queryRunner.query(`
            CREATE INDEX idx_job_info_related_client_id 
            ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_INFO}(related_client_id)
        `);
        
        await queryRunner.query(`
            CREATE INDEX idx_job_info_related_service_frequency_id 
            ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_INFO}(related_service_frequency_id)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes first
        await queryRunner.query(`
            DROP INDEX IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.idx_job_info_related_client_id
        `);
        
        await queryRunner.query(`
            DROP INDEX IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.idx_job_info_related_service_frequency_id
        `);
        
        // Drop the table
        await queryRunner.query(`
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_INFO}
        `);
    }
}