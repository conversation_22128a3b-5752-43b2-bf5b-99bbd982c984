import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class CreateCompanyAllowedDomain1708444800000 implements MigrationInterface {
    name = "CreateCompanyAllowedDomain1708444800000";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ALLOWED_DOMAIN} (
                "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                "company_id" INTEGER NOT NULL,
                "allowed_domain" VARCHAR(300) NOT NULL,
                "api_key" text NOT NULL
            );

            CREATE INDEX "IDX_COMPANY_ALLOWED_DOMAIN_company_id" ON 
            ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ALLOWED_DOMAIN} ("company_id");
            CREATE INDEX "IDX_COMPANY_ALLOWED_DOMAIN_allowed_domain" ON 
            ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ALLOWED_DOMAIN} ("allowed_domain");
            CREATE INDEX "IDX_COMPANY_ALLOWED_DOMAIN_composite" ON 
            ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ALLOWED_DOMAIN} ("allowed_domain", "company_id");
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX "IDX_COMPANY_ALLOWED_DOMAIN_company_id";
            DROP INDEX "IDX_COMPANY_ALLOWED_DOMAIN_allowed_domain";
            DROP INDEX "IDX_COMPANY_ALLOWED_DOMAIN_composite";

            DROP TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_ALLOWED_DOMAIN};
        `);
    }
}