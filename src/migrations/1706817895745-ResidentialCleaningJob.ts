import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class ResidentialCleaningJobs1706817895745 implements MigrationInterface {
    name = 'ResidentialCleaningJobs1706817895745'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE} 
            (id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
            "type" text NOT NULL,
            "start_time" TIMESTAMPTZ NOT NULL,
            "end_time" TIMESTAMPTZ NOT NULL,
            "status" text NOT NULL,
            "related_service_area_id" uuid,
            "related_service_type_id" uuid,
            "related_client_id" bigint,
            "related_company_id" bigint,
            "extras" JSONB NOT NULL DEFAULT '[]',
            "total_price" DECIMAL(10,2) NOT NULL,
            "created_datetime" TIMESTAMP NOT NULL DEFAULT now(), 
            "updated_datetime" TIMESTAMP NOT NULL DEFAULT now());
            
            CREATE INDEX "idx_residential_job_status" 
                ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE} ("status");
            CREATE INDEX "idx_residential_job_client" 
                ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE} ("related_client_id");
            CREATE INDEX "idx_residential_job_company" 
                ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE} ("related_company_id");
            `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            -- Drop indexes
            DROP INDEX IF EXISTS "idx_residential_job_status";
            DROP INDEX IF EXISTS "idx_residential_job_client";
            DROP INDEX IF EXISTS "idx_residential_job_company";
            DROP INDEX IF EXISTS "idx_residential_job_per_employee_job";
            DROP INDEX IF EXISTS "idx_residential_job_per_employee_employee";

            -- Drop tables
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE}`);
    }
}
