import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class CreateJobAssignedEmployeeTable1717675598000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create the table
        await queryRunner.query(`
            CREATE TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_EMPLOYEE} (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "related_job_id" uuid NOT NULL,
                "related_employee_id" integer NOT NULL,
                CONSTRAINT "PK_job_assigned_employee" PRIMARY KEY ("id"),
                CONSTRAINT "UQ_job_assigned_employee_job_employee" UNIQUE ("related_job_id", "related_employee_id")
            );
        `);

        await queryRunner.query(`
            CREATE INDEX idx_job_assigned_employee_related_job_employee 
            ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_EMPLOYEE}(related_job_id, related_employee_id)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`
            DROP INDEX IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.idx_job_assigned_employee_related_job_employee
        `);

        // Drop the table
        await queryRunner.query(`
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_EMPLOYEE};
        `);
    }
}