import { MigrationInterface, QueryRunner } from 'typeorm';
import { JobType, CurrencyType } from '../types';
import { DB_TABLE_NAMES, DB_SCHEMA_NAMES } from '../constants/db_tables';

export class CreateTypeOfServiceTable1736303580116 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create the table with raw SQL
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_TYPE_OF_SERVICE} (
                id uuid NOT NULL DEFAULT uuid_generate_v4(),
                type_of_service_id UUID DEFAULT uuid_generate_v4() NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                type_of_cleaning text NOT NULL,
                price DECIMAL(10, 2),
                currency text NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                related_company_id INT
            );
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the table with raw SQL
        await queryRunner.query(`
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_TYPE_OF_SERVICE};
        `);
    }
}