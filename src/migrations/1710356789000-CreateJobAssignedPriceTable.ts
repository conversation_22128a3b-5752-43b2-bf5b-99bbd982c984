import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class CreateJobAssignedPriceTable1710356789000 implements MigrationInterface {
    
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create the table using raw SQL
        await queryRunner.query(`
            CREATE TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_PRICE}(
                "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                "job_id" UUID NOT NULL,
                "total_price" DECIMAL(10,2) NOT NULL,
                "created_datetime" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_datetime" TIMESTAMP NOT NULL DEFAULT now()
            );
        `);
        
        // Add comment to the table
        await queryRunner.query(`
            COMMENT ON TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_PRICE} IS 'Stores pricing information assigned to jobs';
        `);
        
        // Add comments to columns
        await queryRunner.query(`
            COMMENT ON COLUMN ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_PRICE}."id" IS 'Primary key identifier';
            COMMENT ON COLUMN ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_PRICE}."job_id" IS 'Foreign key reference to the job table';
            COMMENT ON COLUMN ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_PRICE}."total_price" IS 'Total price assigned to the job';
            COMMENT ON COLUMN ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_PRICE}."created_datetime" IS 'Timestamp when record was created';
            COMMENT ON COLUMN ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_PRICE}."updated_datetime" IS 'Timestamp when record was last updated';
        `);
        
        // Create an index on job_id for faster lookups
        await queryRunner.query(`
            CREATE INDEX "IDX_job_assigned_price_job_id" ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_PRICE}("job_id");
        `);
    
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the index
        await queryRunner.query(`
            DROP INDEX IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_PRICE}."IDX_job_assigned_price_job_id";
        `);
        
        // Drop the table
        await queryRunner.query(`
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_PRICE};
        `);
    }
}