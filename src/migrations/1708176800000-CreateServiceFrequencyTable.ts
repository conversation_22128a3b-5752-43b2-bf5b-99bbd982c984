import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class CreateServiceFrequencyTable1708176800000 implements MigrationInterface {
    name = 'CreateServiceFrequencyTable1708176800000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create the table with raw SQL
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_SERVICE_FREQUENCY} (
                id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
                name varchar(100) NOT NULL,
                description varchar(300) NOT NULL,
                frequency_name varchar(255) NOT NULL,
                frequency_interval integer,
                frequency_count integer,
                type_of_cleaning text NOT NULL,
                price DECIMAL(10, 2),
                discount integer, 
                currency text NOT NULL,
                is_active boolean DEFAULT TRUE,
                related_company_id integer NOT NULL,
                created_datetime TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_datetime TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);

        await queryRunner.query(`
            CREATE INDEX idx_service_frequency_company_active 
            ON ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_SERVICE_FREQUENCY}(type_of_cleaning, related_company_id, is_active)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.idx_service_frequency_company_active
        `);
        // Drop the table with raw SQL
        await queryRunner.query(`
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_SERVICE_FREQUENCY};
        `);
    }
}
