import { MigrationInterface, QueryRunner } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../constants/db_tables";

export class CreateJobAssignedExtraTable1615815125000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create the table
        await queryRunner.query(`
            CREATE TABLE ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_EXTRA} (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                job_id UUID,
                related_extra_id UUID NOT NULL,
                amount INTEGER,
                created_datetime TIMESTAMP NOT NULL DEFAULT now(),
                updated_datetime TIMESTAMP NOT NULL DEFAULT now(),
                
                CONSTRAINT fk_job
                    FOREIGN KEY (job_id)
                    REFERENCES ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.RES_CLEANING_JOB_TABLE}(id)
                    ON DELETE CASCADE
                    ON UPDATE CASCADE,
                
                CONSTRAINT fk_extra
                    FOREIGN KEY (related_extra_id)
                    REFERENCES ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.COMPANY_JOB_EXTRA}(id)
            );
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the table
        await queryRunner.query(`
            DROP TABLE IF EXISTS ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.JOB_ASSIGNED_EXTRA};
        `);
    }
}