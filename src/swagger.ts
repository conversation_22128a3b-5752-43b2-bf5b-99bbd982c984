import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';
import { getMetadataArgsStorage } from 'routing-controllers';
import { validationMetadatasToSchemas } from 'class-validator-jsonschema';
import { defaultMetadataStorage } from 'class-transformer/cjs/storage';
import { routingControllersToSpec } from 'routing-controllers-openapi';
import session from 'express-session';
import rateLimit from 'express-rate-limit';

const generateSwaggerDoc = () => {
    const storage = getMetadataArgsStorage();
    const schemas = validationMetadatasToSchemas({
        refPointerPrefix: '#/components/schemas/',
        classTransformerMetadataStorage: defaultMetadataStorage
    })
    
    return routingControllersToSpec(
        storage,
        {},
        {
            openapi: '3.1.0',
            servers: [
                {
                    url: 'https://midori-be.onrender.com/api',  // Your HTTPS URL
                    description: 'Production server'
                },
                {
                    url: 'http://localhost:3032/api',    // Local development
                    description: 'Local development server'
                }
            ],
            components: {
                schemas: schemas as any,
                securitySchemes: {
                    bearerAuth: {
                        type: 'http',
                        scheme: 'bearer',
                        bearerFormat: 'JWT',
                        in: 'header',
                        description: 'Enter your JWT token'
                    }
                }
            },
            security: [
                {
                    bearerAuth: []
                }
            ],
            info: {
                title: 'Midori API Documentation',
                version: '0.1.0',
                description: 'API documentation for Midori backend',
            }
        }
    );
}

// Initial setup
let swaggerSpec = generateSwaggerDoc();


export const setupSwaggerUi = (app: Express) => {
    const loginLimiter = rateLimit({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 10, // Limit each IP to 5 login attempts per window
        standardHeaders: true,
        legacyHeaders: false,
        handler: function (
            req: any,
            res: any,
            next: any,
            options: any
        ) {
            res.status(429).render('swagger-login', {
                error: 'Too many login attempts. Please try again after 15 minutes.'
            });
        },
        skipFailedRequests: false,
        skipSuccessfulRequests: true
    });

    const swaggerOptions = {
        swaggerOptions: {
            persistAuthorization: true,
        }
    };

    const checkAuth = (req, res, next) => {
        if ((req.session as any).authenticated) {
            next();
        } else {
            res.redirect('/api-docs/login');
        }
    };

    app.use(session({
        secret: process.env.SESSION_KEY,
        resave: false,
        saveUninitialized: true,
        cookie: {
            secure: process.env.NODE_ENV === 'production', // Only use secure cookies in production
            httpOnly: true,
            maxAge: 24 * 60 * 60 * 1000 // 24 hours
        }
    }));
    app.get('/api-docs/login', (req, res) => {
        res.render('../views/swagger-login', { error: req.query.error });
    });
    
    app.post('/api-docs/login', (req, res) => {
        const { username, password } = req.body;
        
        // Replace with your actual authentication logic
        if (username === process.env.SWAGGER_USER && password === process.env.SWAGGER_PASSWORD) {
            (req.session as any).authenticated = true;
            req.session.save((err) => {
                if (err) {
                    console.error('Session save error:', err);
                    return res.redirect('/api-docs/login?error=Session error');
                }
                res.redirect('/api-docs');
            });
        } else {
            res.redirect('/api-docs/login?error=Invalid credentials');
        }
    });

    app.use('/api-docs', checkAuth, swaggerUi.serve, (req, res, next) => {
        swaggerSpec = generateSwaggerDoc();
        swaggerUi.setup(swaggerSpec, swaggerOptions)(req, res, next)
    });
    app.use('/swagger.json', checkAuth, (_, res) => {
        res.json(swaggerSpec)
    })
}