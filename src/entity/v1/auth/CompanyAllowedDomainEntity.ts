import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";
import { DB_TABLE_NAMES, DB_SCHEMA_NAMES } from "../../../constants/db_tables";

@Entity({name:DB_TABLE_NAMES.COMPANY_ALLOWED_DOMAIN, schema: DB_SCHEMA_NAMES.MIDORI})
@Index(["allowed_domain", "company_id"])
export class CompanyAllowedDomainEntity {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column()
    company_id: number;

    @Index()
    @Column()
    allowed_domain: string;

    @Column()
    api_key: string;
}