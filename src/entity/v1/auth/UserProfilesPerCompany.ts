import { ViewColumn, ViewEntity } from "typeorm";
import { DB_SCHEMA_NAMES } from "../../../constants/db_tables";

@ViewEntity({
    expression: `
      SELECT profile_id, related_company_id, related_user_id FROM ${DB_SCHEMA_NAMES.MIDORI}.admin_profile
      UNION
      SELECT profile_id, related_company_id, related_user_id FROM ${DB_SCHEMA_NAMES.MIDORI}.client_profile
      UNION
      SELECT profile_id, related_company_id, related_user_id FROM ${DB_SCHEMA_NAMES.MIDORI}.employee_profile
    `,
  })
export default class UserProfilesPerCompany {
    @ViewColumn()
    related_user_id: number;

    @ViewColumn()
    profile_id: number;
  
    @ViewColumn()
    related_company_id: number;
}