import { <PERSON><PERSON><PERSON>, CreateDate<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";
import { IsNotEmpty } from "class-validator";
import AdminProfileEntity from "../profile/AdminProfileEntity";
import EmployeeProfileEntity from "../profile/EmployeeProfileEntity";
import ClientProfileEntity from "../profile/ClientProfileEntity";

@Entity({name:DB_TABLE_NAMES.USER_AUTH_TABLE, schema: DB_SCHEMA_NAMES.MIDORI})
export default class UserAuthEntity{
    @PrimaryGeneratedColumn()
    user_id?: number

    @Column({ unique: true })
    @IsNotEmpty()
    username?: string

    @Column()
    @IsNotEmpty()
    password?: string
    
    @CreateDateColumn({ 
        name: 'created_datetime',
        type: 'timestamp without time zone',
        default: Date.now() 
    })
    @IsNotEmpty()
    created_datetime?: Date;

    @UpdateDateColumn({ 
        name: 'updated_datetime',
        type: 'timestamp without time zone',
        default: Date.now() 
    })
    @IsNotEmpty()
    updated_datetime?: Date;

    @Column()
    @IsNotEmpty()
    role?: string;

    @OneToOne(() => AdminProfileEntity, admin_profile_info => admin_profile_info.user_credentials, { cascade: true })
    admin_profile_info?: AdminProfileEntity;

    @OneToOne(() => ClientProfileEntity, client_profile_info => client_profile_info.user_credentials, { cascade: true })
    client_profile_info?: ClientProfileEntity;

    @OneToOne(() => EmployeeProfileEntity, employee_profile_info => employee_profile_info.user_credentials, { cascade: true })
    employee_profile_info?: EmployeeProfileEntity;
}