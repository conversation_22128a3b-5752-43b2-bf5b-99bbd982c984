import { CreateDateC<PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryColumn } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";
import { IsNotEmpty } from "class-validator";

@Entity({name:DB_TABLE_NAMES.REFRESH_TOKEN_TABLE, schema: DB_SCHEMA_NAMES.MIDORI})
export default class RefreshTokenEntity {
    @PrimaryColumn()
    @IsNotEmpty()
    refresh_token: string;
    
    @CreateDateColumn({ 
        name: 'created_datetime',
        type: 'timestamp without time zone',
    })
    @IsNotEmpty()
    created_datetime: Date;
}