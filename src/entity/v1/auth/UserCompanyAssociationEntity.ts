import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { DB_TABLE_NAMES, DB_SCHEMA_NAMES } from "../../../constants/db_tables";
import { UserRoles } from "../../../constants";

@Entity({name:DB_TABLE_NAMES.USER_COMPANY_ASSOCIATIONS, schema: DB_SCHEMA_NAMES.MIDORI})
@Index(['userId', 'companyId'])
@Index(['profileId', 'role'])
export class UserCompanyAssociationEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'company_id' })
  companyId: number;

  @Column({ name: 'profile_id' })
  profileId: number;

  @Column({
    type: 'enum',
    enum: UserRoles
  })
  role: UserRoles;

  @CreateDateColumn({ name: 'created_datetime' })
  created_datetime: Date;

  @UpdateDateColumn({ name: 'updated_datetime' })
  updated_datetime: Date;
}