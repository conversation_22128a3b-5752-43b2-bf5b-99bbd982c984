import { <PERSON><PERSON>oa<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import { CurrencyType } from "../../../types";
import { AdditionalDetailsEntity } from "./AdditionalDetailsEntity";
import { DB_TABLE_NAMES, DB_SCHEMA_NAMES } from "../../../constants/db_tables";
import { JobAssignedAdditionalDetailsEntity } from "../job/JobAssignedAdditionalDetailsEntity";

@Entity({name: DB_TABLE_NAMES.COMPANY_ADDITIONAL_DETAILS_CHOICES, schema:DB_SCHEMA_NAMES.MIDORI})
export class AdditionalDetailsChoicesEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ 
        type: 'decimal',
        precision: 10,
        scale: 2,
    })
    price: number;

    @Column({
        type: 'enum',
        enum: CurrencyType,
        name: 'currency'
    })
    currency: Cur<PERSON>cyType;

    @Column('uuid')
    related_additional_detail_id: string;

    @ManyToOne(() => AdditionalDetailsEntity, details => details.choices, {onDelete: 'CASCADE', onUpdate: 'CASCADE'})
    @JoinColumn({name: 'related_additional_detail_id'})
    additionalDetails: AdditionalDetailsEntity;

    @OneToMany(() => JobAssignedAdditionalDetailsEntity, assignedDetail => assignedDetail.choice, {onDelete: 'CASCADE', onUpdate: 'CASCADE'})
    assigned_job_additional_detail: JobAssignedAdditionalDetailsEntity;

    @AfterLoad()
    convertToNumbers() {
        this.price = parseFloat(this.price as unknown as string);
    }
}