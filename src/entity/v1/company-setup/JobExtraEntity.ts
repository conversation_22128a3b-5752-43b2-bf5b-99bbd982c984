import { <PERSON><PERSON>oad, Column, CreateDate<PERSON><PERSON>umn, <PERSON>tity, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";
import { CurrencyType, JobType } from "../../../types";
import { JobAssignedExtraEntity } from "../job/JobAssignedExtraEntity";

@Entity({name:DB_TABLE_NAMES.COMPANY_JOB_EXTRA, schema: DB_SCHEMA_NAMES.MIDORI})
export class JobExtraEntity{

    @PrimaryGeneratedColumn("uuid")
    id: string;

    @Column()
    name: string;

    @Column({ 
        type: 'decimal',
        precision: 10,
        scale: 2,
    })
    price: number;

    @Column({
        type: 'enum',
        enum: CurrencyType,
        name: 'currency',
        nullable: true
    })
    currency: CurrencyType;

    @Column()
    description: string;

    @Column({
        type: 'enum',
        enum: JobType,
        name: 'type_of_cleaning'
    })
    type_of_cleaning: JobType;

    @Column({default: false})
    is_active: boolean;

    @Column()
    available_amount: number;

    @Column()
    related_company_id: number;
    
    @CreateDateColumn({ name: 'created_datetime' })
    created_datetime: Date;

    @UpdateDateColumn({ name: 'updated_datetime' })
    updated_datetime: Date;

    @OneToMany(() => JobAssignedExtraEntity, assignedExtra => assignedExtra.extra, {cascade: true})
    assigned_job_configuration: JobAssignedExtraEntity;

    @AfterLoad()
    convertToNumbers() {
        this.price = parseFloat(this.price as unknown as string);
    }
}