import { <PERSON><PERSON><PERSON>d, <PERSON>umn, <PERSON>reateDate<PERSON><PERSON>umn, Entity, <PERSON>in<PERSON><PERSON><PERSON>n, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { CurrencyType, JobType } from "../../../types";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";
import { JobAssignedConfigurationEntity } from "../job/JobAssignedConfigurationEntity";

@Entity({name: DB_TABLE_NAMES.COMPANY_SERVICE_PROPERTY_SETUP, schema:DB_SCHEMA_NAMES.MIDORI})
export class ServicePropertySetupEntity{
    @PrimaryGeneratedColumn("uuid")
    id: string;

    @Column()
    name: string;
    
    @Column({nullable: true})
    description: string;

    @Column()
    available_amount: number;

    @Column({
        type: 'enum',
        enum: JobType,
        name: 'type_of_cleaning'
    })
    type_of_cleaning: JobType;
    
    @Column({nullable: true})
    price: number;
    
    @Column({
        type: 'enum',
        enum: CurrencyType,
        name: 'currency',
        nullable: true
    })
    currency: CurrencyType;

    @Column()
    is_active: boolean;

    @Column({name: "related_company_id"})
    related_company_id: number;

    @CreateDateColumn({ name: 'created_datetime' })
    created_datetime: Date;

    @UpdateDateColumn({ name: 'updated_datetime' })
    updated_datetime: Date;

    @OneToMany(() => JobAssignedConfigurationEntity, config => config.service_property_setup, {cascade: true})
    assigned_job_configuration: JobAssignedConfigurationEntity;

    @AfterLoad()
    convertToNumbers() {
        this.price = parseFloat(this.price as unknown as string);
    }
}