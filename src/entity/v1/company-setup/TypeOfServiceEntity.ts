import { AfterLoad, Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { CurrencyType, JobType } from "../../../types";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";
import { ScheduledJobDetailsEntity } from "..";

@Entity({name: DB_TABLE_NAMES.COMPANY_TYPE_OF_SERVICE, schema:DB_SCHEMA_NAMES.MIDORI})
export class TypeOfServiceEntity {
  @PrimaryGeneratedColumn("uuid")
    id: string;

    @Column({ type: 'text' })
    name: string

    @Column({ type: 'text' })
    description: string;

    @Column({
        type: 'enum',
        enum: JobType,
        name: 'type_of_cleaning'
    })
    type_of_cleaning: JobType

    @Column({ 
        type: 'decimal',
        precision: 10,
        scale: 2,
    })
    price: number

    @Column({
        type: 'enum',
        enum: CurrencyType,
        name: 'currency'
    })
    currency: CurrencyType

    @Column()
    is_active: boolean

    @CreateDateColumn({ name: 'created_at' })
    created_at: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updated_at: Date;

    @Column({name: "related_company_id"})
    related_company_id: number;

    @OneToOne(() => ScheduledJobDetailsEntity, job => job.service_type)
    job: ScheduledJobDetailsEntity;

    @AfterLoad()
    convertToNumbers() {
        this.price = parseFloat(this.price as unknown as string);
    }
}