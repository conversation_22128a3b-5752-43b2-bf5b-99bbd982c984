import { AfterLoad, Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { CurrencyType, JobType } from "../../../types";
import { DB_TABLE_NAMES, DB_SCHEMA_NAMES } from "../../../constants/db_tables";
import { ScheduledJobDetailsEntity } from "../job";

@Entity({name: DB_TABLE_NAMES.COMPANY_SERVICE_AREA, schema:DB_SCHEMA_NAMES.MIDORI})
export class ServiceAreaEntity {
    @PrimaryGeneratedColumn("uuid")
    id: string;

    @Column()
    sq_ft_from: number;

    @Column()
    sq_ft_to: number;
    
    @Column({
        type: 'enum',
        enum: JobType,
        name: 'type_of_cleaning'
    })
    type_of_cleaning: JobType;

    @Column({ 
        type: 'decimal',
        precision: 10,
        scale: 2,
    })
    price: number

    @Column({
        type: 'enum',
        enum: CurrencyType,
        name: 'currency'
    })
    currency: CurrencyType

    @Column()
    is_active: boolean

    @Column({name: "related_company_id"})
    related_company_id: number

    @CreateDateColumn({ name: 'created_datetime' })
    created_datetime: Date;

    @UpdateDateColumn({ name: 'updated_datetime' })
    updated_datetime: Date;

    @OneToOne(() => ScheduledJobDetailsEntity, scheduledJob => scheduledJob.service_area)
    job: ScheduledJobDetailsEntity;

    @AfterLoad()
    convertToNumbers() {
        this.price = parseFloat(this.price as unknown as string);
    }
}