import { PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Entity, AfterLoad, OneToOne } from "typeorm";
import { CurrencyType, JobType } from "../../../types";
import { DB_TABLE_NAMES, DB_SCHEMA_NAMES } from "../../../constants/db_tables";
import { JobEntity } from "..";

@Entity({name: DB_TABLE_NAMES.COMPANY_SERVICE_FREQUENCY, schema:DB_SCHEMA_NAMES.MIDORI})
export class ServiceFrequencyEntity {
    @PrimaryGeneratedColumn("uuid")
    id: string;
    
    @Column()
    name: string;

    @Column({ 
        type: 'varchar', 
        length: 300 
    })
    description?: string;

    @Column()
    frequency_name: string;

    @Column()
    frequency_interval: number;

    @Column()
    frequency_count: number;
    
    @Column({
        type: 'enum',
        enum: JobType,
        name: 'type_of_cleaning'
    })
    type_of_cleaning: JobType;

    @Column({ 
        type: 'decimal',
        precision: 10,
        scale: 2,
    })
    price: number

    @Column({ 
        type: 'int'
    })
    discount: number

    @Column({
        type: 'enum',
        enum: CurrencyType,
        name: 'currency'
    })
    currency: CurrencyType

    @Column()
    is_active: boolean

    @Column({name: "related_company_id"})
    related_company_id: number

    @CreateDateColumn({ name: 'created_datetime' })
    created_datetime: Date;

    @UpdateDateColumn({ name: 'updated_datetime' })
    updated_datetime: Date;

    @OneToOne(() => JobEntity, job => job.service_frequency)
    job: JobEntity;

    @AfterLoad()
    convertToNumbers() {
        this.price = parseFloat(this.price as unknown as string);
        this.discount = parseFloat(this.discount as unknown as string)/100;
    }
}