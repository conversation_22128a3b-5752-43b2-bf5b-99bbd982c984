import { Column, CreateDate<PERSON>olumn, <PERSON>tity, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { CurrencyType, JobType } from "../../../types";
import { DB_TABLE_NAMES, DB_SCHEMA_NAMES } from "../../../constants/db_tables";
import { AdditionalDetailsChoicesEntity } from "./AdditionalDetailsChoicesEntity";
import { JobAssignedAdditionalDetailsEntity } from "../job/JobAssignedAdditionalDetailsEntity";

@Entity({name: DB_TABLE_NAMES.COMPANY_ADDITIONAL_DETAILS, schema:DB_SCHEMA_NAMES.MIDORI})
export class AdditionalDetailsEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column()
    name: string;
    
    @Column()
    description:string;

    @Column({
        type: 'enum',
        enum: JobType,
        name: 'type_of_cleaning'
    })
    type_of_cleaning: JobType

    @Column()
    is_active: boolean;

    @CreateDateColumn({ name: 'created_at' })
    created_at: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updated_at: Date;

    @Column({name: "related_company_id"})
    related_company_id: number;

    @OneToMany(() => AdditionalDetailsChoicesEntity, choice => choice.additionalDetails, {cascade: true})
    choices: AdditionalDetailsChoicesEntity[];

    @OneToMany(() => JobAssignedAdditionalDetailsEntity, assignedDetail => assignedDetail.additionalDetail, {cascade: true})
    assigned_job_additional_detail: JobAssignedAdditionalDetailsEntity;
}