import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";
import { CompanyProfileEntity } from "./CompanyProfileEntity";
import EmployeeProfileEntity from "./EmployeeProfileEntity";
import ClientProfileEntity from "./ClientProfileEntity";

// @Entity({name:DB_TABLE_NAMES.ADDRESS_PROFILE_TABLE, schema: DB_SCHEMA_NAMES.MIDORI})
export class AddressProfileEntity {

    // @PrimaryGeneratedColumn()
    // address_id?: number

    @Column()
    street: string;

    @Column()
    city: string;

    @Column()
    state: string;

    @Column()
    postal_code: string;

    // @OneToOne(() => CompanyProfileEntity, profile_info => profile_info.address_info)
    // @Join<PERSON><PERSON><PERSON><PERSON>({ name: "related_company_id", referencedColumnName:"company_id"})
    // company_info?: CompanyProfileEntity;

    // @OneToOne(() => EmployeeProfileEntity, emp_profile_info => emp_profile_info.address_info)
    // @JoinColumn({ name: "related_company_id" })
    // employee_address_info?: EmployeeProfileEntity;

    // @OneToOne(() => ClientProfileEntity, client_profile => client_profile.address_info)
    // @JoinColumn({ name: "related_company_id", referencedColumnName: "profile_id" })
    // client_address_info?: ClientProfileEntity;
}