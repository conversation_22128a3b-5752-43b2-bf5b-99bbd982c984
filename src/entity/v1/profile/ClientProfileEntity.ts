import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON> } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";
import ProfileEntity from "./ProfileEntity";
import { IsNotEmpty } from "class-validator";
import JobEntity from "../job/JobEntity";
import UserAuthEntity from "../auth/UserAuthEntity";

@Entity({name:DB_TABLE_NAMES.CLIENT_PROFILE_TABLE, schema: DB_SCHEMA_NAMES.MIDORI})
export default class ClientProfileEntity extends ProfileEntity {

    @Column()
    street: string;

    @Column()
    city: string;

    @Column()
    state: string;

    @Column()
    postal_code: string;

    @Column()
    company_name: string;

    @Column({type: 'bigint'})
    @IsNotEmpty()
    related_company_id: number;

    @OneToOne(() => JobEntity, job_info => job_info.client_info)
    @JoinColumn({name: 'profile_id', referencedColumnName: 'related_client_id'})
    job_info: JobEntity;

    @OneToOne(() => UserAuthEntity, profile_info => profile_info.client_profile_info, {onDelete: 'CASCADE', onUpdate: 'CASCADE'})
    @JoinColumn({ name: 'related_user_id' })
    user_credentials: UserAuthEntity;
}