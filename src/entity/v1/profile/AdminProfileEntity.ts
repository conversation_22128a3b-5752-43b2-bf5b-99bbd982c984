import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "typeorm";
import ProfileEntity from "./ProfileEntity";
import { CompanyProfileEntity } from "./CompanyProfileEntity";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";
import UserAuthEntity from "../auth/UserAuthEntity";

@Entity({name:DB_TABLE_NAMES.ADMIN_PROFILE_TABLE, schema: DB_SCHEMA_NAMES.MIDORI})
export default class AdminProfileEntity extends ProfileEntity {

    @OneToOne(() => CompanyProfileEntity, company_info => company_info.admin_profile, { cascade: true })
    company_info?: CompanyProfileEntity;

    @OneToOne(() => UserAuthEntity, profile_info => profile_info.admin_profile_info, {onDelete: 'CASCADE', onUpdate: 'CASCADE'})
    @JoinColumn({ name: 'related_user_id' })
    user_credentials: User<PERSON>uthEnti<PERSON>;
}