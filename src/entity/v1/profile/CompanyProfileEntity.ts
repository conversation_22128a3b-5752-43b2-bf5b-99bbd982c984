import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";
import AdminProfileEntity from "./AdminProfileEntity";
import { AddressProfileEntity } from "./AddressProfileEntity";

@Entity({name:DB_TABLE_NAMES.COMPANY_PROFILE_TABLE, schema: DB_SCHEMA_NAMES.MIDORI})
export class CompanyProfileEntity {

    @PrimaryGeneratedColumn()
    company_id?: number

    @Column()
    company_name!: string;

    @Column()
    company_website!: string;

    @Column()
    company_phone: string;

    @Column()
    company_email: string;

    @Column()
    street: string;

    @Column()
    city: string;

    @Column()
    state: string;

    @Column()
    postal_code: string;

    @OneToOne(() => AdminProfileEntity, profile_info => profile_info.company_info, {onDelete: 'CASCADE'})
    @JoinColumn({ name: 'related_profile_id' })
    admin_profile: AdminProfileEntity;

    @OneToOne(() => AdminProfileEntity, profile_info => profile_info.company_info, {onDelete: 'CASCADE', onUpdate: 'CASCADE'})
    @JoinColumn({ name: 'related_profile_id' })
    employee_profile: AdminProfileEntity;

    // @OneToOne(() => AdminProfileEntity, profile_info => profile_info.company_info, {onUpdate: 'CASCADE'})
    // @JoinColumn({ name: "related_company_id" })
    // profile_info?: AdminProfileEntity;
}