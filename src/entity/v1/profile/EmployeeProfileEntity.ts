import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToMany, OneToOne } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";
import ProfileEntity from "./ProfileEntity";
import { ProfileUserTypes, UserRoles } from "../../../constants";
import { CompanyProfileEntity } from "./CompanyProfileEntity";
import UserAuthEntity from "../auth/UserAuthEntity";
import { JobAssignedEmployeeEntity } from "../job/JobAssignedEmployeeEntity";

@Entity({name: DB_TABLE_NAMES.EMPLOYEE_PROFILE_TABLE, schema:DB_SCHEMA_NAMES.MIDORI})
export default class EmployeeProfileEntity extends ProfileEntity {

    @Column()
    street: string;

    @Column()
    city: string;

    @Column()
    state: string;

    @Column()
    postal_code: string;

    @Column()
    date_of_birth: string;

    @Column({
        type: "enum",
        enum: UserRoles,
        default: ProfileUserTypes.TECHNICIAN
    })
    position: ProfileUserTypes;

    @Column()
    related_company_id: number;

    @OneToOne(() => CompanyProfileEntity, company_info => company_info.admin_profile)
    company_info?: CompanyProfileEntity;

    @OneToMany(() => JobAssignedEmployeeEntity, job => job.employee_profile)
    assigned_jobs!: JobAssignedEmployeeEntity[];

    @OneToOne(() => UserAuthEntity, profile_info => profile_info.employee_profile_info, {onDelete: 'CASCADE', onUpdate: 'CASCADE'})
    @JoinColumn({ name: 'related_user_id' })
    user_credentials: UserAuthEntity;
}