import { IsNotEmpty } from "class-validator";
import { Base<PERSON>ntity, Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
@Entity()
export default class ProfileEntity extends BaseEntity {
    
    @PrimaryGeneratedColumn()
    profile_id?: number;

    @Column()
    first_name: string;

    @Column()
    last_name: string;

    @Column()
    phone_number: string;

    @Column()
    email: string;

    @Column({type: 'bigint'})
    @IsNotEmpty()
    related_user_id?: number;

    @CreateDateColumn({ 
        name: 'created_datetime',
        type: 'timestamp without time zone',
        default: Date.now() 
    })
    created_datetime?: Date;

    @UpdateDateColumn({ 
        name: 'updated_datetime',
        type: 'timestamp without time zone',
        default: Date.now() 
    })
    updated_datetime?: Date;

    profile_type?: string;
}