import { ServiceFrequencyEntity } from '../company-setup/ServiceFrequencyEntity';
import { 
    Entity, 
    PrimaryGeneratedColumn, 
    Column, BaseEntity, 
    JoinColumn, 
    OneToOne, 
    CreateDateColumn, 
    UpdateDateColumn,
    OneToMany, 
} from "typeorm";
import ClientProfileEntity from "../profile/ClientProfileEntity";
import { JobType } from "../../../types";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";
import ScheduledJobDetailsEntity from './ScheduledJobDetailsEntity';

@Entity({name: DB_TABLE_NAMES.JOB_INFO, schema:DB_SCHEMA_NAMES.MIDORI})
export default class JobEntity {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column()
    type: JobType;

    @Column('uuid')
    related_service_frequency_id: string;

    @OneToOne(() => ServiceFrequencyEntity)
    @JoinColumn({ 
        name: 'related_service_frequency_id',
        referencedColumnName: 'id'
    })
    service_frequency: ServiceFrequencyEntity;

    @Column()
    related_client_id: number;

    @Column()
    related_company_id: number;

    @OneToOne(() => ClientProfileEntity, client_info => client_info.job_info, { eager: true })
    @JoinColumn({name: 'related_client_id', referencedColumnName: 'profile_id'})
    client_info: ClientProfileEntity;

    @OneToMany(() => ScheduledJobDetailsEntity, scheduledJob => scheduledJob.job_info, {cascade: true})
    scheduled_jobs: ScheduledJobDetailsEntity[];

    @Column({ 
        nullable: true,
        type: 'timestamp without time zone',
        default: () => 'CURRENT_TIMESTAMP' 
    })
    last_job_generated_datetime?: Date;

    @Column({ default: false })
    is_active: boolean;

    @CreateDateColumn({ 
        name: 'created_datetime',
        type: 'timestamp without time zone',
        default: () => 'CURRENT_TIMESTAMP' 
    })
    created_datetime!: Date;

    @UpdateDateColumn({ 
        name: 'updated_datetime',
        type: 'timestamp without time zone',
        default: () => 'CURRENT_TIMESTAMP' 
    })
    updated_datetime!: Date;

}
