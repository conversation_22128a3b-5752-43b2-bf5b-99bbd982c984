import { Column, <PERSON>reateDateColumn, DeleteDateColumn, Entity, Index, JoinColumn, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";
import JobEntity from "./JobEntity";
import { Matches } from "class-validator";
import { JobStatus } from "../../../types/types";
import { ServiceAreaEntity } from "../company-setup/ServiceAreaEntity";
import { TypeOfServiceEntity } from "../company-setup/TypeOfServiceEntity";
import { JobAssignedAdditionalDetailsEntity } from "./JobAssignedAdditionalDetailsEntity";
import { JobAssignedConfigurationEntity } from "./JobAssignedConfigurationEntity";
import { JobAssignedExtraEntity } from "./JobAssignedExtraEntity";
import { JobAssignedPriceEntity } from "./JobAssignedPriceEntity";
import { JobAssignedEmployeeEntity } from "./JobAssignedEmployeeEntity";

/**Composite unique index on related_job_id, start_time, end_time to ensure that no two scheduled jobs can have the same start and end time for the same job. */
@Entity({name: DB_TABLE_NAMES.SCHEDULED_JOB, schema:DB_SCHEMA_NAMES.MIDORI})
@Index(['related_job_id', 'start_time', 'end_time'], { unique: true })
export default class ScheduledJobDetailsEntity {

    @PrimaryGeneratedColumn('uuid')
    id: string

 
    @Column({ type: 'timestamptz' })
    @Matches(/^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\+\d{2}:\d{2})?$/, 
        { message: 'Invalid time format. Time must be in 24-hour format (HH:MM:SS).' })
    start_time: Date;

    @Column({ type: 'timestamptz' })
    @Matches(/^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\+\d{2}:\d{2})?$/, 
        { message: 'Invalid time format. Time must be in 24-hour format (HH:MM:SS).' })
    end_time: Date;

    @Column()
    status: JobStatus;

    @Column()
    related_job_id: string;

    @ManyToOne(() => JobEntity, job => job.scheduled_jobs, {
        onDelete: "CASCADE",
        onUpdate: "CASCADE"
    })
    @JoinColumn({name:'related_job_id', referencedColumnName: 'id'})
    job_info: JobEntity;

    @Column('uuid')
    related_service_area_id: string;

    @Column('uuid')
    related_service_type_id: string;
    
    @OneToOne(() => ServiceAreaEntity, serviceArea => serviceArea.job, {
        onDelete: "SET NULL",
    })
    @JoinColumn({ 
        name: 'related_service_area_id',
        referencedColumnName: 'id'
    })
    service_area: ServiceAreaEntity;

    @OneToOne(() => TypeOfServiceEntity, {
        onDelete: "SET NULL",
    })
    @JoinColumn({ 
        name: 'related_service_type_id',
        referencedColumnName: 'id'
    })
    service_type: TypeOfServiceEntity;

    @OneToMany(() => JobAssignedConfigurationEntity, propertySetups => propertySetups.job, {
        cascade: true,
    })
    service_details: JobAssignedConfigurationEntity[];

    @OneToMany(() => JobAssignedExtraEntity, extra => extra.job, {
        cascade: true,
    })
    extras: JobAssignedExtraEntity[];

    @OneToMany(() => JobAssignedAdditionalDetailsEntity, additionalDetails => additionalDetails.job, {
        cascade: true,
    })
    additionalDetails: JobAssignedAdditionalDetailsEntity[];

    // @ManyToMany(() => EmployeeProfileEntity, emp_profile => emp_profile.assigned_jobs)
    // @JoinTable({
    //     name: DB_TABLE_NAMES.RES_JOB_PER_EMPLOYEE,
    //     joinColumn: {
    //         name: "related_job_id",
    //         referencedColumnName: "id"
    //     },
    //     inverseJoinColumn: {
    //         name: "related_employee_profile_id",
    //         referencedColumnName: "profile_id"
    //     }
    // })

    @OneToMany(() => JobAssignedEmployeeEntity, assignedEmployee => assignedEmployee.job, {
        cascade: true,
        eager: true
    })
    assigned_employees: JobAssignedEmployeeEntity[];

    @Column({ 
        type: 'decimal',
        precision: 10,
        scale: 2,
    })
    total_price: number;

    @OneToOne(() => JobAssignedPriceEntity, price => price.scheduled_job, {cascade: true})
    job_price: JobAssignedPriceEntity

    @Column()
    related_company_id: number;

    @CreateDateColumn()
    createdAt?: Date;

    @UpdateDateColumn()
    updatedAt?: Date;
    
    @DeleteDateColumn()
    deletedAt?: Date;
}