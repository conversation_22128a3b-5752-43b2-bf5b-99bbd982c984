import { Column, <PERSON>tity, <PERSON>, Join<PERSON>olumn, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { DB_TABLE_NAMES, DB_SCHEMA_NAMES } from "../../../constants/db_tables";
import EmployeeProfileEntity from "../profile/EmployeeProfileEntity";
import  ScheduledJobDetailsEntity from "./ScheduledJobDetailsEntity";

@Entity({name: DB_TABLE_NAMES.JOB_ASSIGNED_EMPLOYEE, schema:DB_SCHEMA_NAMES.MIDORI})
@Index(['related_job_id', 'related_employee_id'])
export class JobAssignedEmployeeEntity {

    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column('uuid')
    related_job_id: string;

    @Column()
    related_employee_id: number;

    @ManyToOne(() => ScheduledJobDetailsEntity, scheduledJob => scheduledJob.assigned_employees, {
        onDelete: "CASCADE",
        onUpdate: "CASCADE"
    })
    @JoinColumn({name: 'related_job_id'})
    job: ScheduledJobDetailsEntity;

    @ManyToOne(() => EmployeeProfileEntity, {
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        eager: true 
    })
    @JoinColumn({name: 'related_employee_id', referencedColumnName: 'profile_id'})
    employee_profile: EmployeeProfileEntity;

}