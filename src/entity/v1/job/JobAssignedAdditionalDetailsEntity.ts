import { Entity, PrimaryGeneratedColumn, Column, Index, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from "typeorm";
import { DB_TABLE_NAMES, DB_SCHEMA_NAMES } from "../../../constants/db_tables";
import { AdditionalDetailsEntity } from "../company-setup/AdditionalDetailsEntity";
import { AdditionalDetailsChoicesEntity } from "../company-setup/AdditionalDetailsChoicesEntity";
import ScheduledJobDetailsEntity from "./ScheduledJobDetailsEntity";

@Entity({name: DB_TABLE_NAMES.JOB_ASSIGNED_ADDITIONAL_DETAILS, schema:DB_SCHEMA_NAMES.MIDORI})
export class JobAssignedAdditionalDetailsEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;
    
    @Column('uuid')
    job_id: string;

    @Column('uuid')
    @Index({unique:true})
    related_additional_details_id: string;

    @Column('uuid')
    related_additional_details_choice_id: string;

    @CreateDateColumn({ name: 'created_datetime' })
    created_datetime: Date;

    @UpdateDateColumn({ name: 'updated_datetime' })
    updated_datetime: Date;

    @ManyToOne(() => ScheduledJobDetailsEntity, job => job.extras, {
        onDelete: "CASCADE",
        onUpdate: "CASCADE"
    })
    @JoinColumn({name: 'job_id', referencedColumnName: 'id'})
    job: ScheduledJobDetailsEntity;

    @ManyToOne(() => AdditionalDetailsEntity, {
        eager: true 
    })
    @JoinColumn({name: 'related_additional_details_id', referencedColumnName: 'id'})
    additionalDetail: AdditionalDetailsEntity;


    @ManyToOne(() => AdditionalDetailsChoicesEntity, {
        eager: true 
    })
    @JoinColumn({name: 'related_additional_details_choice_id', referencedColumnName: 'id'})
    choice: AdditionalDetailsChoicesEntity;
}