import { ScheduledJobDetailsEntity } from '..';
import { AfterLoad, Column, CreateDateColumn, <PERSON>tity, <PERSON>in<PERSON><PERSON><PERSON>n, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../../constants/db_tables";

@Entity({name: DB_TABLE_NAMES.JOB_ASSIGNED_PRICE, schema:DB_SCHEMA_NAMES.MIDORI})
export class JobAssignedPriceEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;
    
    @Column('uuid')
    job_id: string;
    
    @Column({ 
        type: 'decimal',
        precision: 10,
        scale: 2,
    })
    total_price: number

    @OneToOne(() => ScheduledJobDetailsEntity, scheduledJob => scheduledJob.job_price, {
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        eager:true
    })
    @JoinColumn({ 
        name: 'job_id',
        referencedColumnName: 'id'
    })
    scheduled_job: ScheduledJobDetailsEntity;

    @CreateDateColumn({ name: 'created_datetime' })
    created_datetime: Date;

    @UpdateDateColumn({ name: 'updated_datetime' })
    updated_datetime: Date;

    @AfterLoad()
    convertToNumbers() {
        this.total_price = parseFloat(this.total_price as unknown as string);
    }
}