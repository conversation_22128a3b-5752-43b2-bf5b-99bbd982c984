import { Column, CreateDateColumn, Entity, Index, JoinColumn, ManyToOne, OneToMany, OneToOne, PrimaryColumn, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { DB_TABLE_NAMES, DB_SCHEMA_NAMES } from "../../../constants/db_tables";
import JobEntity from "./JobEntity";
import { ScheduledJobDetailsEntity, ServicePropertySetupEntity } from "..";

@Entity({name: DB_TABLE_NAMES.JOB_ASSIGNED_CONFIGURATION, schema:DB_SCHEMA_NAMES.MIDORI})
export class JobAssignedConfigurationEntity{
    @PrimaryGeneratedColumn('uuid')
    id: string;
    
    @Column('uuid')
    job_id: string;

    @Column('uuid')
    @Index({unique:true})
    related_property_setup_id: string;

    @Column({nullable: true})
    related_additional_details_choice_id: string;

    @Column({nullable: true})
    amount: number;

    @CreateDateColumn({ name: 'created_datetime' })
    created_datetime: Date;

    @UpdateDateColumn({ name: 'updated_datetime' })
    updated_datetime: Date;

    @ManyToOne(() => ScheduledJobDetailsEntity, job => job.service_details, {
        onDelete: "CASCADE",
        onUpdate: "CASCADE"
    })
    @JoinColumn({name: 'job_id', referencedColumnName: 'id'})
    job: ScheduledJobDetailsEntity;

    @ManyToOne(() => ServicePropertySetupEntity, {
        eager: true 
    })
    @JoinColumn({name: 'related_property_setup_id', referencedColumnName: 'id'})
    service_property_setup: ServicePropertySetupEntity;
}