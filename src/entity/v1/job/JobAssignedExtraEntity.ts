import { Entity, PrimaryGeneratedColumn, Column, Index, CreateDateColumn, UpdateDateColumn, ManyToOne, Join<PERSON>olumn } from "typeorm";
import { DB_TABLE_NAMES, DB_SCHEMA_NAMES } from "../../../constants/db_tables";
import JobEntity from "./JobEntity";
import { JobExtraEntity } from "../company-setup/JobExtraEntity";
import { ScheduledJobDetailsEntity } from "..";

@Entity({name: DB_TABLE_NAMES.JOB_ASSIGNED_EXTRA, schema:DB_SCHEMA_NAMES.MIDORI})
export class JobAssignedExtraEntity{
    @PrimaryGeneratedColumn('uuid')
    id: string;
    
    @Column('uuid')
    job_id: string;

    @Column('uuid')
    @Index({unique:true})
    related_extra_id: string;

    @Column({nullable: true})
    amount: number;

    @CreateDateColumn({ name: 'created_datetime' })
    created_datetime: Date;

    @UpdateDateColumn({ name: 'updated_datetime' })
    updated_datetime: Date;

    @ManyToOne(() => ScheduledJobDetailsEntity, job => job.extras, {
        onDelete: "CASCADE",
        onUpdate: "CASCADE"
    })
    @JoinColumn({name: 'job_id', referencedColumnName: 'id'})
    job: ScheduledJobDetailsEntity;

    @ManyToOne(() => JobExtraEntity, {
        eager: true 
    })
    @JoinColumn({name: 'related_extra_id', referencedColumnName: 'id'})
    extra: JobExtraEntity;
}