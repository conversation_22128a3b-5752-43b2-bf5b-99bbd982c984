import { useExpressServer } from "routing-controllers"
import { AdditionalDetailsController, AdminProfileController, AuthController, ClientProfileController, 
    EmployeeProfileController, JobSpecificationsController, InvitationCode<PERSON>ontroller, JobController,
    ServiceAreaController, ServicePropertySetupController, TypeOfServiceController, BookingFormController,
     ServiceFrequencyController, JobExtraController } from "./controllers"
import { ErrorHandlerMiddleware } from "./middlewares";
import { useContainer } from "routing-controllers";
import { containerAdapter } from "./configs/routingControllerAdapter";


export const setupRoutes = (app) => {

    useContainer(containerAdapter);

    useExpressServer(app, {
        controllers: [AuthController, InvitationCodeController, AdminProfileController, EmployeeProfileController, ClientPro<PERSON>leController, 
            <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>xtraController, TypeOfSer<PERSON><PERSON><PERSON>roll<PERSON>, ServiceAreaController, 
            AdditionalDetailsController, JobSpecificationsController, ServicePropertySetupController, BookingFormController, ServiceFrequencyController],
        middlewares: [ErrorHandlerMiddleware],
        currentUserChecker: async (action) => {
            return action.request.user;
        },
        routePrefix: '/api',
        validation: {
            whitelist: true,
            forbidNonWhitelisted: true,
            forbidUnknownValues: false, 
            skipMissingProperties: false,
            validationError: {
                target: false,
                value: false
            },
            enableDebugMessages: true,
        },
        classTransformer: true,
        defaultErrorHandler: false, // Optional: handle errors yourself.
    })

    // Log routes after initializing them
    console.log(app._router.stack
        .filter(r => r.route)
        .map(r => r.route.path));
}