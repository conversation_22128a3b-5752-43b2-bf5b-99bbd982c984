import app from './app';
import { Logging } from './util';

const logger = new Logging();
const port = process.env.PORT || 3032;

async function startServer() {
  try {
    const application = await app.initializeApp();
    
    return application.listen(port, () => {
      logger.info(`App listening at http://localhost:${port}`);
    }).on('error', (e: any) => logger.error(e));
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();