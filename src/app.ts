import "reflect-metadata";
import express, { Express } from 'express';
import path from 'path';
import cors from 'cors';
import passport from './configs/passport.config';
import cookieParser from 'cookie-parser'
import { setupSwaggerUi } from './swagger';
import { setupRoutes } from './router';
import { Logging } from "./util";
import { setupViews } from "./views";
import { setupProcessHandlers } from "./process-handlers";
import { setupCronJobs } from "./cron";


export class App {
  private app: Express;
  private logger = new Logging();

  
  constructor(){
    this.app = express();
  }

  public async initializeApp(): Promise<Express> {
    this.app.use(cors({
      origin: process.env.NODE_ENV === 'production' 
        ? process.env.ALLOWED_ORIGIN 
        : 'http://localhost:5173',
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
      credentials: true
    }))
    
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    this.app.use(cookieParser());
    this.app.use(express.static(path.join(__dirname, '../public')));
    this.app.use(passport.initialize())
    
    const cronManager = await setupCronJobs();
    setupViews(this.app);
    setupRoutes(this.app);
    setupSwaggerUi(this.app);
    setupProcessHandlers(cronManager, this.logger);

    return this.app;
  }

  public getApp(): Express {
    return this.app;
  }
}

export default new App();
