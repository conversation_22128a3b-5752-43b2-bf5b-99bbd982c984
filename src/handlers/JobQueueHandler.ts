import { JobService } from "../services";
import { IQueueHandler } from "./IQueueHandler";
import { container } from '../configs/container';
import { EventType, Event, JobEventData } from "../types/event";

export class JobQueueHandler implements IQueueHandler {
    private readonly jobService: JobService;

    constructor() {
        this.jobService = container.resolve('jobService');
    }

    async handleData(event: Event<EventType.JOB, JobEventData>): Promise<boolean> {
        // Process data queue item
        return true;
    }
}
  