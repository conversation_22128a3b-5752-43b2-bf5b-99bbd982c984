import { AppDatabaseClient } from "../../configs";
import { ClientProfileEntity } from "../../entity/v1";
import { Logging } from "../../util";

export default class ClientProfileRepository {

    constructor(
        private readonly logger: Logging,
        private clientProfileRepository = AppDatabaseClient.getRepository(ClientProfileEntity),
    ){}

    public async findClientProfileByProfileId(profileId: number):Promise<ClientProfileEntity>{
        this.logger.debug(`Find client profile by profileId: ${profileId}`);
        return await this.clientProfileRepository.findOneByOrFail({profile_id: profileId});
    }

    public async findClientProfilesByCompanyId(companyId: number): Promise<ClientProfileEntity[]>{
        this.logger.debug(`Find clients profiles by companyId: ${companyId}`);
        return await this.clientProfileRepository.find({
            where: {related_company_id: companyId}
        })
    }

    public async findClientProfileByUserId(userId: number): Promise<ClientProfileEntity>{
        this.logger.debug(`Find clients profile by userId: ${userId}`);
        return await this.clientProfileRepository.findOneByOrFail(
            {related_user_id: userId}
        )
    }

    public async createClientProfileByCompanyId(entity: ClientProfileEntity): Promise<ClientProfileEntity>{
        this.logger.debug(`Create client profile by companyId: ${entity.related_company_id}`);
        if(!await this.clientProfileRepository.existsBy({related_user_id: entity.related_user_id, related_company_id: entity.related_company_id})){
            return await this.clientProfileRepository.save(entity);
        }
    }


    public async updateClientProfileByCompanyId(entity: ClientProfileEntity): Promise<ClientProfileEntity>{
        this.logger.debug(`Update client profile by companyId: ${entity.related_company_id}`);
        await this.clientProfileRepository.save(entity);
        return this.clientProfileRepository.findOneBy({profile_id: entity.profile_id});
    }

    public async deleteClientProfileByProfileId(profileId: number): Promise<any>{
        this.logger.debug(`Delete employee profile by profileId: ${profileId}`);
        return await this.clientProfileRepository.delete({profile_id: profileId})
    }
}