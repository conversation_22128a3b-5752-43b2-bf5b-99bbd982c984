import AdminProfileEntity from "../../entity/v1/profile/AdminProfileEntity";
import { Logging } from "../../util";
import { AppDatabaseClient } from "../../configs";
import { UserAuthEntity } from "../../entity/v1";

export default class AdminProfileRepository {

    constructor(
        private readonly logger: Logging,
        private profileRepository = AppDatabaseClient.getRepository(AdminProfileEntity),
    ){}
    
    public async findAdminProfileByUserId(userId: number): Promise<AdminProfileEntity>{
        this.logger.debug(`Find admin profile by userId: ${userId}`);
        return this.profileRepository.findOneOrFail({
            where: {related_user_id: userId},
            relations: ['company_info']
        });
    }

    public async findAdminProfileByProfileId(profileId: number): Promise<AdminProfileEntity>{
        this.logger.debug(`Find admin profile by profileId: ${profileId}`);
        return this.profileRepository.findOneOrFail({
            where: {profile_id: profileId},
            relations: ['company_info']
        });
    }

    public async createUserProfileInfo(entity: AdminProfileEntity): Promise<AdminProfileEntity>{
        this.logger.debug(`Create new profile with name: ${entity.first_name}`);
        return await this.profileRepository.save(entity);
    }


    public async updateProfileAndCompanyInfo(newEntity: AdminProfileEntity): Promise<AdminProfileEntity>{
        this.logger.debug(`Update profile with name: ${newEntity.first_name}`);
        await this.profileRepository.save(newEntity)
        return this.findAdminProfileByUserId(newEntity.related_user_id);
    }

    public async deleteProfileAndCompanyInfo(userId: number): Promise<any>{
        this.logger.debug(`Delete profile with userId: ${userId}`);
        return await this.profileRepository.delete({ related_user_id: userId})
    }
}