import { In } from "typeorm";
import { AppDatabaseClient } from "../../configs";
import { EmployeeProfileEntity } from "../../entity/v1";
import { Logging } from "../../util";

export default class EmployeeProfileRepository {

    constructor(
        private readonly logger: Logging = new Logging(),
        private employeeProfileRepository = AppDatabaseClient.getRepository(EmployeeProfileEntity),
    ){}

    public async findEmployeeProfileByProfileId(profileId:number): Promise<EmployeeProfileEntity>{
        this.logger.debug(`Find employee profiles by profileId: ${profileId}`);
        return await this.employeeProfileRepository.findOneByOrFail({profile_id: profileId})
    }

    public async findEmployeeProfilesByCompanyId(companyId: number): Promise<EmployeeProfileEntity[]>{
        this.logger.debug(`Find employee profiles by companyId: ${companyId}`);
        return await this.employeeProfileRepository.find({
            where: {related_company_id: companyId}
        })
    }

    public async findEmployeeProfilesByIds(profileIds: number[]): Promise<EmployeeProfileEntity[]> {
        return await this.employeeProfileRepository.find({
            where:{
                profile_id: In(profileIds)
            }
        })
    }

    public async findEmployeeProfileByUserId(userId: number): Promise<EmployeeProfileEntity>{
        return await this.employeeProfileRepository.findOneByOrFail(
            {related_user_id: userId}
        )
    }

    public async createEmployeeProfileByCompanyId(entity: EmployeeProfileEntity): Promise<EmployeeProfileEntity>{
        this.logger.debug(`Create employee profile by companyId: ${entity.related_company_id}`);
        const profileExists = await this.employeeProfileRepository.existsBy({related_user_id: entity.related_user_id, related_company_id: entity.related_company_id});
        if(!profileExists){
            return await this.employeeProfileRepository.save(entity);
        }
    }

    public async updateEmployeeProfileByCompanyId(entity: EmployeeProfileEntity): Promise<EmployeeProfileEntity>{
        this.logger.debug(`Update employee profile by companyId: ${entity.related_company_id}`);
        await this.employeeProfileRepository.save(entity);
        return await this.employeeProfileRepository.findOneBy({profile_id: entity.profile_id})
    }

    public async deleteEmployeeProfileByProfileId(profileId: number): Promise<any>{
        this.logger.debug(`Delete employee profile by profileId: ${profileId}`);
        return await this.employeeProfileRepository.delete({profile_id: profileId})
    }
}