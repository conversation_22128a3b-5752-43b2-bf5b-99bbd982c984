import { In } from "typeorm";
import { AppDatabaseClient } from "../../configs";
import { AdditionalDetailsChoicesEntity, AdditionalDetailsEntity } from "../../entity/v1";
import { JobType } from "../../types";

export class AdditionalDetailsRepository {
    constructor(
        private additionalDetailsRepository = AppDatabaseClient.getRepository(AdditionalDetailsEntity),
        private additionalChoicesRepository = AppDatabaseClient.getRepository(AdditionalDetailsChoicesEntity)
    ){}

    async createAdditionalDetailForCompany(entity: AdditionalDetailsEntity): Promise<AdditionalDetailsEntity> {
        return await this.additionalDetailsRepository.save(entity);
    }

    async updateAdditionalDetailForCompanyPerId(entity: AdditionalDetailsEntity): Promise<AdditionalDetailsEntity> {
        await this.additionalDetailsRepository.save(entity);
        return await this.additionalDetailsRepository.findOneOrFail({
            relations: {
                choices: true
            },
            where:{id:entity.id, related_company_id: entity.related_company_id }
        });
    }

    async findAllAdditionalDetailsForCompany(companyId: number, offset: number, limit: number):Promise<[AdditionalDetailsEntity[], number]>{
        return await this.additionalDetailsRepository.findAndCount({
            relations: {
                choices: true
            },
            where:{related_company_id: companyId, is_active: true},
            skip: offset,
            take: limit
        });
    }

    async findAllAdditionalDetailsForCompanyNoLimit(companyId: number, type?: JobType, active?: boolean):Promise<AdditionalDetailsEntity[]>{
        return await this.additionalDetailsRepository.find({
            relations: {
                choices: true
            },
            where:{related_company_id: companyId, type_of_cleaning: type, is_active: active},
            order: {
                name: 'ASC'
            },
        });
    }

    async findAdditionalDetailForCompanyPerId(additionalDetailId: string, companyId: number):Promise<AdditionalDetailsEntity>{
        return await this.additionalDetailsRepository.findOneOrFail({
            relations: {
                choices: true
            },
            where:{id: additionalDetailId, related_company_id: companyId}
        });
    }

    async findAdditionalDetailChoicePriceById(additionalChoiceIds: string[]): Promise<AdditionalDetailsChoicesEntity[]> {
        return await this.additionalChoicesRepository.find({
            select: ['id','price','currency'],
            where: {id: In(additionalChoiceIds)}
        });
    }

    async deleteAdditionalDetailForCompanyPerId(additionalDetailId: string, companyId: number){
        return await this.additionalDetailsRepository.delete({id: additionalDetailId, related_company_id: companyId});
    }

}