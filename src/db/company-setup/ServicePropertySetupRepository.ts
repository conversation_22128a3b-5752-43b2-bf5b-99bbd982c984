import { In } from 'typeorm';
import { AppDatabaseClient } from "../../configs";
import { ServicePropertySetupEntity } from "../../entity/v1";
import { JobType } from "../../types";

export class ServicePropertySetupRepository {

    constructor(
        private readonly servicePropertySetupRepository = AppDatabaseClient.getRepository(ServicePropertySetupEntity)
    ){}

    async findAllServicePropertySetupForCompany(companyId: number, offset: number, limit: number): Promise<[ServicePropertySetupEntity[], number]> {
        return this.servicePropertySetupRepository.findAndCount({
            where:{related_company_id: companyId, is_active: true},
            skip: offset,
            take: limit
        });
    }

    async findAllServicePropertySetupForCompanyWithoutLimit(companyId: number, type?: JobType, active?: boolean): Promise<ServicePropertySetupEntity[]>{
        return this.servicePropertySetupRepository.findBy({related_company_id: companyId, is_active: active, type_of_cleaning: type});
    }

    async findServicePropertySetupByID(servicePropertySetupId: string, companyId: number): Promise<ServicePropertySetupEntity> {
        return this.servicePropertySetupRepository.findOneByOrFail({
            id: servicePropertySetupId,
            related_company_id: companyId
        });
    }

    async findServicePropertySetupPriceById(servicePropertySetupIds: string[], companyId: number): Promise<ServicePropertySetupEntity[]> {
        return this.servicePropertySetupRepository.find({
            select: ['id','price','currency'],
            where: {id: In(servicePropertySetupIds), related_company_id: companyId}
        });
    }

    async createServicePropertySetup(entity: ServicePropertySetupEntity): Promise<ServicePropertySetupEntity> {
        return this.servicePropertySetupRepository.save(entity);
    }

    async updateServicePropertySetupById(entity: ServicePropertySetupEntity): Promise<ServicePropertySetupEntity> {
        await this.servicePropertySetupRepository.save(entity);
        return this.servicePropertySetupRepository.findOneByOrFail({id: entity.id, related_company_id: entity.related_company_id}); 
    }

    async deleteServicePropertySetupById(servicePropertySetupId: string, companyId: number){
        return this.servicePropertySetupRepository.delete({id: servicePropertySetupId, related_company_id: companyId});
    }

}