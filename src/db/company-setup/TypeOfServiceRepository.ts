import { AppDatabaseClient } from "../../configs";
import { TypeOfServiceEntity } from "../../entity/v1";
import { JobType } from "../../types";

export class TypeOfServiceRepository {

    constructor(
        private typeOfServiceRepository = AppDatabaseClient.getRepository(TypeOfServiceEntity),

    ){}

    async createNewTypeOfServicePerCompanyId(entity: TypeOfServiceEntity): Promise<TypeOfServiceEntity>{
        return this.typeOfServiceRepository.save(entity);
    }

    async updateTypeOfServicePerId(entity: TypeOfServiceEntity): Promise<TypeOfServiceEntity>{
        await this.typeOfServiceRepository.save(entity);
        return this.typeOfServiceRepository.findOneByOrFail({id: entity.id, related_company_id: entity.related_company_id});
    }

    async findAllTypeOfServicesForCompany(companyId: number, offset: number, limit: number): Promise<[TypeOfServiceEntity[], number]>{
        return this.typeOfServiceRepository.findAndCount({
            where:{related_company_id: companyId, is_active: true},
            skip: offset,
            take: limit
        })
    }

    async findAllTypeOfServicesForCompanyWithoutLimit(companyId: number, type?: JobType, active?: boolean):Promise<TypeOfServiceEntity[]>{
        return this.typeOfServiceRepository.findBy({related_company_id: companyId, type_of_cleaning: type, is_active: active});
    }

    async findTypeOfServiceByServiceId(typeOfServiceId: string, companyId: number): Promise<TypeOfServiceEntity>{
        return this.typeOfServiceRepository.findOneByOrFail({id: typeOfServiceId, related_company_id: companyId});
    }

    async findServiceTypePriceById(typeOfServiceId: string, companyId: number): Promise<TypeOfServiceEntity> {
        return this.typeOfServiceRepository.findOneOrFail({
            select: ['id','price','currency'],
            where: {id: typeOfServiceId, related_company_id: companyId}
        });
    }

    async deleteTypeOfServiceByServiceId(typeOfServiceId: string, companyId: number){
        return await this.typeOfServiceRepository.delete({id: typeOfServiceId, related_company_id: companyId});
    }
}