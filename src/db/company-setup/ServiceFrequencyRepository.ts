import { AppDatabaseClient } from "../../configs";
import { ServiceFrequencyEntity } from "../../entity/v1";
import { JobType } from "../../types";

export class ServiceFrequencyRepository {
    constructor(
        private readonly serviceFrequencyRepository = AppDatabaseClient.getRepository(ServiceFrequencyEntity),
    ){}

    async findServiceFrequencyForCompany(
        companyId: number, offset: number, limit: number, isActive: boolean
    ): Promise<[ServiceFrequencyEntity[], number]> {
        return await this.serviceFrequencyRepository.findAndCount({
            where:{related_company_id: companyId, is_active: isActive},
            skip: offset,
            take: limit
        });
    }

    async findServiceFrequencyForCompanyByJobType(
        companyId: number, type: JobType, isActive: boolean
    ): Promise<ServiceFrequencyEntity[]>{
        return await this.serviceFrequencyRepository.find({
            where: {type_of_cleaning: type, related_company_id: companyId, is_active: isActive}
        });
    }

    async findServiceFrequencyForCompanyById(id: string, companyId: number, isActive?: boolean){
        return await this.serviceFrequencyRepository.findOneOrFail({
            where: {id, related_company_id: companyId, is_active: isActive}
        });
    }

    async createServiceFrequencyForCompany(entity: ServiceFrequencyEntity) {
        return await this.serviceFrequencyRepository.save(entity);
    }

    async updateServiceFrequencyForCompany(entity: Partial<ServiceFrequencyEntity>) {
        await this.serviceFrequencyRepository.save(entity);
        return this.findServiceFrequencyForCompanyById(entity.id, entity.related_company_id);
    }

    async deleteServiceFrequencyForCompany(id: string, companyId: number) {
        return this.serviceFrequencyRepository.delete({id, related_company_id: companyId});
    }
}