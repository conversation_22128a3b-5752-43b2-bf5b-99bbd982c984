import { AppDatabaseClient } from "../../configs/";
import { RefreshTokenEntity, UserAuthEntity, UserCompanyAssociationEntity,  } from "../../entity/v1/";
import { Logging } from "../../util";

export default class AuthPersistance {
    
    constructor(
        private readonly logger?: Logging,
        private authRepository = AppDatabaseClient.getRepository(UserAuthEntity),
        private userAssociationCompanyRepository = AppDatabaseClient.getRepository(UserCompanyAssociationEntity),
        private refreshTokenRepository = AppDatabaseClient.getRepository(RefreshTokenEntity)
    ){}

    findUserCredentialsByUsername = async (username: string): Promise<UserAuthEntity> => {
        this.logger.debug(`Find username: ${username}`);
        return await this.authRepository.findOne({
            where: {'username': username},
        });
    }

    findUserCredentialsById = async (userId: String): Promise<any> => {
        this.logger.debug(`Find username: ${userId}`);
        return await this.authRepository
        .createQueryBuilder('user_auth')
        .where('user_auth.user_id=:userId', {userId})
        .getOne()
    }

    findUserAssociationByUserId = async (userId: number): Promise<UserCompanyAssociationEntity> => {
        this.logger.debug(`Find username: ${userId}`);
        return await this.userAssociationCompanyRepository.findOneByOrFail({userId: userId})
    }

    createRefreshTokenRecord = async (refreshToken: string) => {
        this.logger.debug(`Added refreshToken: ${refreshToken}`);
        const refreshTokenEntity = new RefreshTokenEntity();
        refreshTokenEntity.refresh_token = refreshToken;
        refreshTokenEntity.created_datetime = new Date();
        return await this.refreshTokenRepository.save(refreshTokenEntity);
    }
}