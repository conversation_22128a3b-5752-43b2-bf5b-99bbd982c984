import { AppDatabaseClient } from "../../configs";
import { CompanyAllowedDomainEntity } from "../../entity/v1";

export class CompanyAllowedDomainRepository {

    constructor(
        private readonly repository = AppDatabaseClient.getRepository(CompanyAllowedDomainEntity)
    ){}

    async findPartnerIdByKeyAndDomain(apiKey: string, domain: string): Promise<CompanyAllowedDomainEntity> {
        return await this.repository.findOneOrFail({
            where: {allowed_domain: domain, api_key: apiKey}
        })
    }
}