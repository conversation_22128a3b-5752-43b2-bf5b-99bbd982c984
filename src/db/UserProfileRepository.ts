import { AppDatabaseClient } from "../configs";
import { HTTP_ERROR_TYPES } from "../constants";
import { UserAuthEntity } from "../entity/v1";
import { AuthenticationError } from "../errors";
import { Logging } from "../util";


export default class UserProfileRepository {
    constructor(
        private readonly logger?: Logging,
        private readonly userProfileRepository = AppDatabaseClient.getRepository(UserAuthEntity),
    ){}

    checkUsernameExist = async (username: string) => {
        const user = await this.userProfileRepository.findOne({
            where: {'username': username},
        });

        if(user && user.username){
            throw new AuthenticationError({
                name: HTTP_ERROR_TYPES.USERNAME_ALREADY_EXISTS_ERROR,
                message: 'User already exists'
            });
        }
        return;
    }

    createNewUser = async (user: UserAuthEntity): Promise<UserAuthEntity> => {
        this.logger.debug(`Create new user with username: ${user.username} at ${user.created_datetime}`);
        return await this.userProfileRepository.save(user)
    } 

    updateUserPassword = async (user: UserAuthEntity): Promise<any> => {
        this.logger.debug(`Update password of user with username: ${user.username} at ${user.created_datetime}`);
        const existingUser = await this.userProfileRepository.findOneByOrFail({username: user.username});
        user.user_id = existingUser.user_id;
        return await this.userProfileRepository.save(user);
    }
}