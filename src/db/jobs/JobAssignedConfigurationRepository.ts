import { AppDatabaseClient } from "../../configs";
import { JobAssignedConfigurationEntity } from "../../entity/v1";
import { Logging } from "../../util";

export default class JobAssignedConfigurationRepository {
    constructor(
        private readonly logger: Logging,
        private readonly jobAssignedConfigurationRepository = AppDatabaseClient.getRepository(JobAssignedConfigurationEntity)
    ){}

    async findJobAssignedConfigurationByJobId(jobId: string): Promise<JobAssignedConfigurationEntity[]> {
        this.logger.info(`Retrieving job assigned config for jobId: ${jobId}`);
        return this.jobAssignedConfigurationRepository.find({where: {job_id: jobId}});
    }
}