import { AppDatabaseClient } from "../../configs";
import { JobAssignedExtraEntity } from "../../entity/v1";
import { Logging } from "../../util";

export class JobAssignedExtraRepository {
    constructor(
        private readonly logger: Logging,
        private readonly jobAssignedConfigurationRepository = AppDatabaseClient.getRepository(JobAssignedExtraEntity)
    ){}

    async findJobAssignedExtraByJobId(jobId: string): Promise<JobAssignedExtraEntity[]> {
        this.logger.info(`Retrieving job assigned config for jobId: ${jobId}`);
        return this.jobAssignedConfigurationRepository.find({where: {job_id: jobId}});
    }
}