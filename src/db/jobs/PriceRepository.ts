import { Repository } from "typeorm";
import { AppDatabaseClient } from "../../configs";
import { JobAssignedPriceEntity } from "../../entity/v1";

export class PriceRepository {

    private priceRepository: Repository<JobAssignedPriceEntity>;

    constructor(){
        this.priceRepository = AppDatabaseClient.getRepository(JobAssignedPriceEntity);
    }

    async findByJobId(jobId: string): Promise<JobAssignedPriceEntity> {
        return await this.priceRepository.findOneByOrFail({job_id: jobId})
    }
    
    async upsertPriceForJob(jobId: string, totalPrice: number): Promise<JobAssignedPriceEntity> {
        const entity = new JobAssignedPriceEntity();
        entity.job_id = jobId;
        entity.total_price = totalPrice;
        return await this.priceRepository.save(entity);
    }
    deletePriceForJob(jobId: string) {
        return this.priceRepository.delete({job_id: jobId});
    }

}