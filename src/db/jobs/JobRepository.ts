import { <PERSON>, <PERSON>ete<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MoreThanOr<PERSON>qua<PERSON> } from "typeorm";
import { AppDatabaseClient } from "../../configs";
import { JobEntity, ScheduledJobDetailsEntity } from "../../entity/v1";
import { Logging } from "../../util";
import { JobStatus } from "../../types/types";
import { DB_SCHEMA_NAMES, DB_TABLE_NAMES } from "../../constants/db_tables";

interface ExplainAnalyzeResult {
    'QUERY PLAN': string;
}

export default class JobRepository{
    constructor(
        private readonly logger,
        private jobEntityRepository = AppDatabaseClient.getRepository(JobEntity),
        private scheduledJobRepository =  AppDatabaseClient.getRepository(ScheduledJobDetailsEntity)
    ){}

    /**
     * Finds all scheduled jobs for a specific company with pagination.
     * 
     * @param companyId - ID of the company to find scheduled jobs for
     * @param offset - Number of records to skip (pagination)
     * @param limit - Maximum number of records to return
     * @returns Promise resolving to [scheduledJobs, totalCount]
     */
    async findScheduledJobsForCompanyByPage(
        companyId: number, 
        offset: number, 
        limit: number, 
        startDate: Date, 
        endDate: Date
    ): Promise<[ScheduledJobDetailsEntity[], number]> {
        this.logger.debug(`Finding scheduled jobs for company: ${companyId}`);

        const whereClause: any = {
            related_company_id: companyId
        };

        if (startDate && endDate) {
            whereClause.start_time = Between(startDate, endDate);
            this.logger.debug('Filtering with date range', { startDate, endDate });
        } else if (startDate && !endDate) {
            whereClause.start_time = MoreThanOrEqual(startDate);
            this.logger.debug('Filtering from start date onwards', { startDate });
        } else if (!startDate && endDate) {
            whereClause.start_time = LessThanOrEqual(endDate);
            this.logger.debug('Filtering up to end date', { endDate });
        }
    
        // Direct query without needing to join job_info just for company ID
        return await this.scheduledJobRepository.findAndCount({
            where: whereClause,
            relations: {
                job_info: {
                    client_info: true,
                    service_frequency: true
                },
                assigned_employees: true
            },
            select: {
                id: true,
                job_info: {
                    type: true,
                    client_info: {
                        profile_id: true,
                        first_name: true,
                        last_name: true
                    },
                    service_frequency: {
                        id: true,
                        frequency_name: true
                    }
                },
                start_time: true,
                status: true,
                assigned_employees: {
                    employee_profile: {
                        profile_id: true,
                        first_name: true,
                        last_name: true
                    }
                }
            },
            skip: offset,
            take: limit,
            order: {
                start_time: 'ASC' // Add ordering for consistent results
            }
        });
    }

    /**
     * Finds all scheduled jobs for a company within a date range.
     * 
     * @param companyId - ID of the company to find scheduled jobs for
     * @param startDate - Start date for the range (inclusive)
     * @param endDate - End date for the range (inclusive)
     * @returns Promise resolving to an array of scheduled job entities
     */
    async findScheduledJobsByDateRange(companyId: number, startDate: Date, endDate: Date): Promise<ScheduledJobDetailsEntity[]> {
        this.logger.debug(`Finding scheduled jobs for company ${companyId} between ${startDate} and ${endDate}`);

        return await this.scheduledJobRepository.find({
            where: {
              start_time: Between(startDate, endDate),
              job_info: {
                related_company_id: companyId
              }
            },
            relations: {
              job_info: {
                client_info: true
              }
            },
            select: {
              id: true,
              status: true,
              start_time: true,
              end_time: true,
              job_info: {
                id: true,
                client_info: {
                  profile_id: true,
                  first_name: true,
                  last_name: true
                }
              }
            },
            order: {
                start_time: 'ASC' // Add ordering for consistent results
            }
          });
    }

    findScheduledJobForCompanyById = async (id: string, companyId: number) => {
        this.logger.debug(`Get residential cleaning job: ${id} for company: ${companyId}`);

        return await this.scheduledJobRepository.findOneOrFail({
          where: {
            id,
            related_company_id: companyId
          },
          relations: {
            job_info: {
              client_info: true,
              service_frequency: true
            },
            service_area: true,
            service_type: true,
            service_details: {
              service_property_setup: true
            },
            extras: {
              extra: true
            },
            additionalDetails: {
              additionalDetail: true,
              choice: true
            },
            assigned_employees: true
          }
        });
      }

    async findJobInfoById(id: string, companyId: number) {
        this.logger.debug(`Get job info by id: ${id} for company with id: ${companyId}`);

        return await this.jobEntityRepository.findOneOrFail({
            where: {id, related_company_id: companyId},
            relations: {
                client_info: true,
                scheduled_jobs: {
                    service_area: true,
                    service_type: true,
                    service_details: true,
                    extras: true,
                    additionalDetails: true,
                    assigned_employees: true
                },
                service_frequency: true
            }
        })

    }

    /**
     * Finds all scheduled jobs for a specific client and company.
     * Optimized to reduce data transfer and improve query performance.
     * 
     * @param clientId - ID of the client to find scheduled jobs for
     * @param companyId - ID of the company the jobs belong to
     * @returns Promise resolving to an array of ScheduledJobDetailsEntity objects
     */
    async findScheduledJobsForClientAndCompany(clientId: number, companyId: number): Promise<ScheduledJobDetailsEntity[]> {
        this.logger.debug(`Finding scheduled jobs for client: ${clientId} and company: ${companyId}`);
        
        // First, find all job IDs for this client and company (lightweight query)
        const jobIds = await this.jobEntityRepository
            .createQueryBuilder('job')
            .select('job.id')
            .where('job.related_client_id = :clientId', { clientId })
            .andWhere('job.related_company_id = :companyId', { companyId })
            .getMany()
            .then(jobs => jobs.map(job => job.id));
        
        if (jobIds.length === 0) {
            return []; // No jobs found, return empty array
        }
        
        // Then, find all scheduled jobs for these job IDs (with needed relations)
        return await this.scheduledJobRepository
            .createQueryBuilder('scheduledJob')
            .leftJoinAndSelect('scheduledJob.service_area', 'service_area')
            .leftJoinAndSelect('scheduledJob.service_type', 'service_type')
            .leftJoinAndSelect('scheduledJob.service_details', 'service_details')
            .leftJoinAndSelect('scheduledJob.extras', 'extras')
            .leftJoinAndSelect('scheduledJob.additionalDetails', 'additionalDetails')
            .leftJoinAndSelect('scheduledJob.assigned_employees', 'assigned_employees')
            .leftJoinAndSelect('scheduledJob.job_info', 'job_info')
            .leftJoinAndSelect('job_info.service_frequency', 'service_frequency')
            .where('scheduledJob.related_job_id IN (:...jobIds)', { jobIds })
            .getMany();
    }

    async findJobsForBatchSize(offset: number, batchSize: number, numMonths: number): Promise <JobEntity[]> {
        this.logger.info(`Get job info entities batch of ${batchSize}`);
        const threeMonthsAgo = new Date();
        threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - numMonths);

        return await this.jobEntityRepository.find({
            where: [
                // Jobs that have never been generated
                {
                    is_active: true,
                    last_job_generated_datetime: IsNull()
                },
                // Jobs that were last generated more than 3 months ago
                {
                    is_active: true,
                    last_job_generated_datetime: LessThan(threeMonthsAgo)
                }
              ],
            relations: {
                service_frequency: true,
                client_info: true
            },
            skip: offset,
            take: batchSize,
            order: {
                last_job_generated_datetime: 'ASC'
            },
        })
    }

    /**
     * Finds the last scheduled job for a specific job ID.
     * Includes error handling for cases where no scheduled job exists.
     *
     * @param jobId - The ID of the job to find the last scheduled job for
     * @returns Promise resolving to the last scheduled job entity or null if none exists
     */
    public async findLastScheduledJobById(jobId: string): Promise<ScheduledJobDetailsEntity | null> {
        try {
            this.logger.debug(`Finding last scheduled job for job ID: ${jobId}`);

            return await this.scheduledJobRepository.findOne({
                where: {related_job_id: jobId},
                relations: {
                    job_info: true,
                    service_area: true,
                    service_type: true,
                    service_details: true,
                    extras: true,
                    additionalDetails: true,
                    assigned_employees: true,
                    job_price: true,
                },
                order: {end_time: 'DESC'}
            });
        } catch (error) {
            this.logger.warn(`Error finding last scheduled job for job ID ${jobId}: ${error.message}`);
            return null;
        }
    }

    /**
     * Finds the last scheduled jobs for multiple job IDs.
     * Optimized for performance with large batches of job IDs.
     *
     * @param jobIds - Array of job IDs to find last scheduled jobs for
     * @returns Promise resolving to an array of ScheduledJobDetailsEntity objects
     */
    public async findLastScheduledJobsByIds(jobIds: string[]): Promise<ScheduledJobDetailsEntity[]> {
        this.logger.info(`Getting last scheduled jobs for ${jobIds.length} job IDs`);

        if (jobIds.length === 0) {
            return [];
        }

        // Use a more efficient approach with a window function if your database supports it
        // This avoids the need for a subquery and is generally more performant
        // Using ROW_NUMBER() window function for better performance
        const results = await this.scheduledJobRepository.query(
            `
            WITH RankedJobs AS (
                SELECT
                    sj.*,
                    ROW_NUMBER() OVER (PARTITION BY sj.related_job_id ORDER BY sj.end_time DESC) as rn
                FROM ${DB_SCHEMA_NAMES.MIDORI}.${DB_TABLE_NAMES.SCHEDULED_JOB} sj
                WHERE sj.related_job_id = ANY($1)
            )
            SELECT * FROM RankedJobs WHERE rn = 1`, 
            [jobIds]);

        // If we have results, load the relations
        if (results.length > 0) {
            const jobIds = results.map((job: { id: string }) => job.id);

            // Load all relations in a single query with proper joins
            return await this.scheduledJobRepository.createQueryBuilder('sj')
                .whereInIds(jobIds)
                .leftJoinAndSelect('sj.job_info', 'job_info')
                .leftJoinAndSelect('sj.service_area', 'service_area')
                .leftJoinAndSelect('sj.service_type', 'service_type')
                .leftJoinAndSelect('sj.service_details', 'service_details')
                .leftJoinAndSelect('sj.extras', 'extras')
                .leftJoinAndSelect('sj.additionalDetails', 'additionalDetails')
                .leftJoinAndSelect('sj.assigned_employees', 'assigned_employees')
                .leftJoinAndSelect('sj.job_price', 'job_price')
                .getMany();
        }

        return [];
    }

    public async createJobEntityForCompanyById (entity: JobEntity){
        this.logger.debug(`Persist created residential cleaning job for client: ${entity.related_client_id}`);
        const newJob = await this.jobEntityRepository.save(entity);
        return await this.findJobInfoById(newJob.id, newJob.related_company_id);
    }

    /**
     * Creates a batch of scheduled jobs efficiently.
     *
     * @param entities - Array of scheduled job entities to create
     * @returns Result of the batch insert operation
     */
    public async createScheduledJobsBatch (entities: ScheduledJobDetailsEntity[]){
        if (entities.length === 0) {
            return;
        }

        this.logger.debug(`Persisting ${entities.length} scheduled jobs`);

        // TypeORM's bulk insert is more efficient than individual inserts
        return await this.scheduledJobRepository
            .createQueryBuilder()
            .insert()
            .values(entities)
            .orIgnore() // This will skip conflicting records
            .execute();
    }

    public async updateScheduledJobPriceInBatchById(ids: string[], totalPrice: number) {
        await this.scheduledJobRepository.createQueryBuilder()
        .update()
        .set({total_price: totalPrice})
        .whereInIds(ids)
        .execute();
    }

    public async updateResidentialCleaningJobByJobId (entity: ScheduledJobDetailsEntity) {
        this.logger.debug(`Persist update residential cleaning job for id: ${entity.id}`);
        const updateEntity = await this.scheduledJobRepository.save(entity);
        return await this.findScheduledJobForCompanyById(updateEntity.id, updateEntity.job_info.related_company_id);
    }

    public async updateJobInfoLastGeneratedTimeById(entity: JobEntity) {
        await this.jobEntityRepository.update(
            { id: entity.id },
            { last_job_generated_datetime: entity.last_job_generated_datetime }
        );
    }

    /**
     * Updates the last generated time for multiple job entities in a batch.
     * Uses a more efficient bulk update approach.
     *
     * @param entities - Array of job entities to update
     */
    public async updateJobInfoLastGeneratedTimeByIdBatch(entities: JobEntity[]) {
        if (entities.length === 0) {
            return;
        }

        this.logger.debug(`Batch updating last_job_generated_datetime for ${entities.length} jobs`);

        // Use a transaction to ensure all updates are atomic
        await AppDatabaseClient.transaction(async transactionalEntityManager => {
            // Group entities by the same timestamp to reduce the number of queries
            const groupedByTimestamp = new Map<string, string[]>();

            entities.forEach(entity => {
                const timestamp = entity.last_job_generated_datetime.toISOString();
                if (!groupedByTimestamp.has(timestamp)) {
                    groupedByTimestamp.set(timestamp, []);
                }
                groupedByTimestamp.get(timestamp).push(entity.id);
            });

            // Execute one update per unique timestamp
            const updatePromises = Array.from(groupedByTimestamp.entries()).map(([timestamp, ids]) => {
                return transactionalEntityManager
                    .createQueryBuilder()
                    .update(JobEntity)
                    .set({ last_job_generated_datetime: new Date(timestamp) })
                    .whereInIds(ids)
                    .execute();
            });

            await Promise.all(updatePromises);
        });
    }

    public async cancelScheduledJobByJobId(id: string, companyId: number): Promise<DeleteResult> {
        return await this.scheduledJobRepository
        .createQueryBuilder()
        .update()
        .set({
          status: JobStatus.CANCELLED
        })
        .where("id = :id", { id })
        .execute();
    }

    public async deleteJobPermanentlyByJobId(id: string, companyId: number): Promise<void>{
        await this.jobEntityRepository.delete({id: id, related_company_id: companyId});
        await this.scheduledJobRepository.delete({related_job_id: id, related_company_id: companyId})
    }

    public async scheduledJobExist(jobId: string): Promise<boolean> {
        return await this.jobEntityRepository.exists({where: {id: jobId}});
    }

}