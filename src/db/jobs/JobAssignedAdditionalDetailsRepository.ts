import { AppDatabaseClient } from "../../configs";
import { JobAssignedAdditionalDetailsEntity } from "../../entity/v1";
import { Logging } from "../../util";

export class JobAssignedAdditionalDetailsRepository {
    constructor(
        private readonly logger: Logging,
        private readonly jobAssignedConfigurationRepository = AppDatabaseClient.getRepository(JobAssignedAdditionalDetailsEntity)
    ){}

    async findJobAssignedExtraByJobId(jobId: string): Promise<JobAssignedAdditionalDetailsEntity[]> {
        this.logger.info(`Retrieving job assigned config for jobId: ${jobId}`);
        return this.jobAssignedConfigurationRepository.find({where: {job_id: jobId}});
    }
}