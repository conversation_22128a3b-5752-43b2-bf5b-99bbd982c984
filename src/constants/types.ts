export enum ServiceFrequencyEnum {
    DAILY="DAILY",
    WEEKLY="WEEKLY",
    MONTHLY="MONTHLY",
    YEARLY="YEARLY"
}


export const RRuleFrequency = {
    ONCE: {
        rruleFreq: 'DAILY',  // Special case, using <PERSON><PERSON><PERSON><PERSON> with COUNT=1
        defaultCount: 1,
        defaultInterval: 1,
    },
    DAILY: {
        rruleFreq: 'DAILY',
        defaultInterval: 1,
        defaultCount: undefined
    },
    WEEKLY: {
        rruleFreq: 'WEEKLY',
        defaultInterval: 1,
        defaultCount: undefined
    },
    BIWEEKLY: {
        rruleFreq: 'WEEKLY',
        defaultInterval: 2,
        defaultCount: undefined
    },
    MONTHLY: {
        rruleFreq: 'MONTHLY',
        defaultInterval: 1,
        defaultCount: undefined
    }
}

export type ServiceRRuleFrequency = keyof typeof RRuleFrequency;