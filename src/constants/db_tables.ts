export const DB_SCHEMA_NAMES = {
    MIDORI: "midori"
}
export const DB_TABLE_NAMES = {
    USER_AUTH_TABLE:`user_auth_info`,
    USER_ASSING_ROLE_TABLE:`user_assign_role`,
    REFRESH_TOKEN_TABLE:`refresh_tokens`,
    USER_PER_COMPANY:`user_per_company`,
    ADMIN_PROFILE_TABLE: `admin_profile`,
    EMPLOYEE_PROFILE_TABLE: `employee_profile`,
    CLIENT_PROFILE_TABLE: `client_profile`,
    ADDRESS_PROFILE_TABLE: `address_profile`,
    COMPANY_PROFILE_TABLE: `company_profile`,
    RES_CLEANING_JOB_TABLE: `residential_cleaning_jobs`,
    RES_CLEANING_JOB_EXTRAS_TABLE: `residential_cleaning_job_extras_info`,
    RES_CLEANING_EXTRA_PER_JOB_TABLE:`residential_cleaning_extra_per_job`,
    RES_JOB_PER_EMPLOYEE: `residential_job_per_employee`,
    JO<PERSON>_INFO: `job_info`,
    SCHEDULED_JOB: `scheduled_job`,
    JOB_ASSIGNED_CONFIGURATION:`job_assigned_configuration`,
    JOB_ASSIGNED_EXTRA: `job_assigned_extra`,
    JOB_ASSIGNED_ADDITIONAL_DETAILS: `job_assigned_additional_details`,
    JOB_ASSIGNED_PRICE: 'job_assigned_price',
    JOB_ASSIGNED_EMPLOYEE: 'job_assigned_employee',
    COMPANY_TYPE_OF_SERVICE: `company_type_of_service`,
    USER_COMPANY_ASSOCIATIONS: `user_company_associations`,
    COMPANY_SERVICE_AREA: `company_service_area`,
    COMPANY_ADDITIONAL_DETAILS: `company_additional_details`,
    COMPANY_ADDITIONAL_DETAILS_CHOICES: `company_additional_details_choices`,
    COMPANY_SERVICE_PROPERTY_SETUP: `company_service_property_setup`,
    COMPANY_SERVICE_FREQUENCY: `company_service_frequency`,
    COMPANY_ALLOWED_DOMAIN: `company_allowed_domain`,
    COMPANY_JOB_EXTRA: `company_job_extra`
}

export default {DB_TABLE_NAMES}