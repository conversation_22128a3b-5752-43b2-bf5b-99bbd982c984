import AdminProfileRepository from "../../db/profile/AdminProfileRepository";
import { AdminProfileDto } from "../../dto/v1";
import {  UserAuthEntity } from "../../entity/v1";
import AdminProfileEntity from "../../entity/v1/profile/AdminProfileEntity";
import { UserRequestObj } from "../../types";
import { InvitationCodeGenerator, Logging, mapper } from "../../util";
import bcrypt from 'bcryptjs';
import { UserProfileRepository } from "../../db";
import AuthService from "../authentication/AuthService";

export default class ProfileService {

    constructor(
        protected readonly logger?: Logging,
        private readonly adminProfilePersistance: AdminProfileRepository = new AdminProfileRepository(logger),
        private readonly authService: AuthService = new AuthService(logger),
        private readonly userProfileRepository: UserProfileRepository = new UserProfileRepository(logger),
    ){}

    retrieveAdminProfileByUserId = async (userId: number): Promise<AdminProfileDto> => {
        const profileInfoEntity: AdminProfileEntity = await this.adminProfilePersistance.findAdminProfileByUserId(userId);
        return mapper.mapAdminProfileEntityToDto(profileInfoEntity);
    }

    createAdminProfileByProfileId = async (user: UserRequestObj, dto: AdminProfileDto): Promise<any> => {
        const newProfileEntity: AdminProfileEntity = mapper.mapAdminProfileDtoToProfileEntity(user, dto);
        const newUser: UserAuthEntity = await this.__createNewUserCred(dto.credentials.username, dto.credentials.password, dto.invitationCode, newProfileEntity);
        const { accessToken, refreshToken } = await this.authService.generateTokens(newUser, newUser.admin_profile_info.company_info.company_id);
        const adminProfileDto: AdminProfileDto = mapper.mapAdminProfileEntityToDto(newUser.admin_profile_info);
        return {
            adminProfileDto,
            accessToken,
            refreshToken
        }
    }

    updateAdminProfileByProfileId = async (user: UserRequestObj, dto: AdminProfileDto): Promise<AdminProfileDto> => {
        if(dto.credentials.password){
            await this.__updateUserCred(dto.credentials.username, dto.credentials.password);
        }
        const updatedAdminProfile: AdminProfileEntity = mapper.mapAdminProfileDtoToProfileEntity(user, dto);
        const response: AdminProfileEntity = await this.adminProfilePersistance.updateProfileAndCompanyInfo(updatedAdminProfile);
        return mapper.mapAdminProfileEntityToDto(response);
    }

    deleteAdminProfileByProfileId = async (user: UserRequestObj): Promise<AdminProfileDto> => {
        return await this.adminProfilePersistance.deleteProfileAndCompanyInfo(user.userId);
    }

    __createNewUserCred = async (username: string, password: string, invitation_code: string, profile: AdminProfileEntity): Promise<UserAuthEntity> => {
        await this.userProfileRepository.checkUsernameExist(username);
        const role: string = InvitationCodeGenerator.verifyInvitationCode(invitation_code);
        const hashedPassword: string = await bcrypt.hash(password, 10);
        const user: UserAuthEntity = {
            username,
            password: hashedPassword,
            role,
            admin_profile_info: profile
        }

        return await this.userProfileRepository.createNewUser(user);
    }

    __updateUserCred = async (username: string, password: string): Promise<UserAuthEntity> => {
        const hashedPassword: string = await bcrypt.hash(password, 10);
        const user: UserAuthEntity = {
            username,
            password: hashedPassword,
        }
        return await this.userProfileRepository.updateUserPassword(user);
    }
}
