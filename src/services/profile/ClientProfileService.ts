import { ClientProfileRepository, UserProfileRepository } from "../../db";
import { ClientProfileEntity, UserAuthEntity } from "../../entity/v1";
import { Logging, mapper } from "../../util";
import IService from "../IService";
import { HTTP_ERROR_TYPES, UserRoles } from "../../constants";
import { ClientProfileDto } from "../../dto/v1";
import { UserRequestObj } from "../../types";
import { ProfileError } from "../../errors";
import bcrypt from 'bcryptjs';

export default class ClientProfileService implements IService {

    constructor(
        protected readonly logger?: Logging,
        private readonly clientProfileRepository: ClientProfileRepository = new ClientProfileRepository(logger),
        private readonly userProfileRepository: UserProfileRepository = new UserProfileRepository(logger),
    ){}

    retrieveClientProfileByProfileId = async (profileId: number): Promise<ClientProfileDto> => {
        const profileData: ClientProfileEntity = await this.clientProfileRepository.findClientProfileByProfileId(profileId);
        return mapper.mapClientProfileEntityToDto(profileData);
    }

    retrieveClientProfilesByCompanyId = async (user: UserRequestObj): Promise<ClientProfileDto[]> => {
        const profilesData: ClientProfileEntity[] = await this.clientProfileRepository.findClientProfilesByCompanyId(user.companyId);
        try{
            return profilesData.map((profile) => mapper.mapClientProfileEntityToDto(profile));
        }catch(error){
            throw new ProfileError({ 
                name: HTTP_ERROR_TYPES.PROFILE_NOT_FOUND_ERROR, 
                message: "Profiles not found for the company"
            })
        }
    }

    createClientProfileByCompanyId = async (user: UserRequestObj, dto: ClientProfileDto): Promise<any> => {
        const newProfileEntity: ClientProfileEntity = mapper.mapClientProfileDtoToProfileEntity(user, dto);
        const newUser: UserAuthEntity = await this.__createNewUserCred(dto.credentials.username, dto.credentials.password, newProfileEntity);
        return mapper.mapClientProfileEntityToDto(newUser.client_profile_info);
    }

    updateClientProfileByCompanyId = async (user: UserRequestObj, dto: ClientProfileDto): Promise<ClientProfileDto | void> => {
        if(dto.credentials.password){
            await this.__updateUserCred(dto.credentials.username, dto.credentials.password);
        }

        const updatedClientProfile: ClientProfileEntity = mapper.mapClientProfileDtoToProfileEntity(user, dto);
        const response: ClientProfileEntity = await this.clientProfileRepository.updateClientProfileByCompanyId(updatedClientProfile);
        return mapper.mapClientProfileEntityToDto(response);
    }

    deleteClientProfileByProfileId = async (profileId: number): Promise<void> => {
        return await this.clientProfileRepository.deleteClientProfileByProfileId(profileId);
    }

    __createNewUserCred = async (username: string, password: string, profile: ClientProfileEntity): Promise<UserAuthEntity> => {
        await this.userProfileRepository.checkUsernameExist(username);
        const role: string = UserRoles.CLIENT;
        const hashedPassword: string = await bcrypt.hash(password, 10);
        const user = {
            username,
            password: hashedPassword,
            role,
            client_profile_info: profile
        }
        return await this.userProfileRepository.createNewUser(user);
    }

    __updateUserCred = async (username: string, password: string): Promise<UserAuthEntity> => {
        const hashedPassword: string = await bcrypt.hash(password, 10);
        const user: UserAuthEntity = {
            username,
            password: hashedPassword
        }  

        return await this.userProfileRepository.updateUserPassword(user);
    }
}
