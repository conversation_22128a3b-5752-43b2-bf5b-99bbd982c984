import { HTTP_ERROR_TYPES, UserRoles } from "../../constants";
import { EmployeeProfileRepository, UserProfileRepository } from "../../db";
import { EmployeeProfileDto } from "../../dto/v1";
import { EmployeeProfileEntity, UserAuthEntity } from "../../entity/v1";
import { ProfileError } from "../../errors";
import { UserRequestObj } from "../../types";
import { Logging, mapper } from "../../util";
import IService from "../IService";
import bcrypt from 'bcryptjs';

export default class EmployeeProfileService implements IService {

    constructor(
        protected readonly logger?: Logging,
        private readonly employeeProfileRepository: EmployeeProfileRepository = new EmployeeProfileRepository(logger),
        private readonly userProfileRepository: UserProfileRepository = new UserProfileRepository(logger),

    ){}

    retrieveEmployeeProfileByProfileId = async (profileId: number): Promise<EmployeeProfileDto> => {
        const profile: EmployeeProfileEntity = await this.employeeProfileRepository.findEmployeeProfileByProfileId(profileId);
        return mapper.mapEmployeeEntityToEmployeeProfileDto(profile);
    }

    retrieveEmployeeProfilesByCompanyId = async (user: UserRequestObj): Promise<EmployeeProfileDto[]> => {
        const profilesData: EmployeeProfileEntity[] = await this.employeeProfileRepository.findEmployeeProfilesByCompanyId(user.companyId);
        try{
            return profilesData.map((profile) => mapper.mapEmployeeEntityToEmployeeProfileDto(profile));
        }catch(error){
            throw new ProfileError({ 
                name: HTTP_ERROR_TYPES.PROFILE_NOT_FOUND_ERROR, 
                message: "Profiles not found for the company"
            })
        }
    }

    createEmployeeProfileByCompanyId = async (user: UserRequestObj, dto: EmployeeProfileDto): Promise<EmployeeProfileDto> => {
        const newProfileEntity: EmployeeProfileEntity = mapper.mapEmployeeProfileDtoToProfileEntity(user, dto);
        const newUser: UserAuthEntity = await this.__createNewUserCred(dto.credentials.username, dto.credentials.password, newProfileEntity);
        return mapper.mapEmployeeEntityToEmployeeProfileDto(newUser.employee_profile_info);
    }

    updateEmployeeProfileByCompanyId = async (user: UserRequestObj, dto: EmployeeProfileDto): Promise<EmployeeProfileDto | void> => {
        if(dto.credentials.password){
            await this.__updateUserCred(dto.credentials.username, dto.credentials.password);
        }
        const employeeProfile: EmployeeProfileEntity = mapper.mapEmployeeProfileDtoToProfileEntity(user, dto);

        const profile = await this.employeeProfileRepository.updateEmployeeProfileByCompanyId(employeeProfile);
        return mapper.mapEmployeeEntityToEmployeeProfileDto(profile);

    }

    deleteEmployeeProfileByProfileId = async (profileId: number): Promise<void> => {
        return await this.employeeProfileRepository.deleteEmployeeProfileByProfileId(profileId);
    }

    __createNewUserCred = async (username: string, password: string, profile: EmployeeProfileEntity): Promise<UserAuthEntity> => {
        await this.userProfileRepository.checkUsernameExist(username);
        const role: string = UserRoles.EMPLOYEE;
        const hashedPassword: string = await bcrypt.hash(password, 10);
        const user =  {
            username,
            password: hashedPassword,
            role,
            employee_profile_info: profile
        }
        return await this.userProfileRepository.createNewUser(user);
    }

    __updateUserCred = async (username: string, password: string): Promise<UserAuthEntity> => {
        const hashedPassword: string = await bcrypt.hash(password, 10);
        const user: UserAuthEntity = {
            username,
            password: hashedPassword
        }  

        return await this.userProfileRepository.updateUserPassword(user);
    }
}
