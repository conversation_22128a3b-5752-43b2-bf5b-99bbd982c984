// src/services/jobQueue.ts
import PgBoss from 'pg-boss';
import { createPgBossInstance } from '../configs/pgboss.config';
import { Logging } from '../util';
import { Event } from '../types/event';
import { QueueName } from '../types/queue';

const queueOptions = {
  // Polling options
  newJobCheckInterval: 1000,     // Check for new jobs every 1000ms
  
  // Job fetch options  
  teamSize: 3,                   // Number of workers (from your existing code)
  teamConcurrency: 1,            // Jobs per worker
  
  // Retry configuration
  retryLimit: 5,                 // Maximum retry attempts
  retryDelay: 1000,              // Delay between retries (ms)
  retryBackoff: true             // Exponential backoff
};

export class QueueService {
  private boss: PgBoss;

  constructor(
    private readonly logger: Logging = new Logging(),
  ) {
    this.boss = createPgBossInstance();
    this.boss.start();
  }

  async produceEvent<T, D>(queueName: QueueName, event: Event<T, D>): Promise<string[]> {
    // Queue the job for future processing
    this.logger.info(`Put the new job in processing queue with id ${event.id}`);
    return this.boss.publish(queueName, {
      ...event,
      queuedAt: new Date()
    }, {
      retryLimit: 5,
      retryDelay: 1000,
      retryBackoff: true,
      startAfter: 0  // Set this to any delay you want in seconds
    });
  }

  async consumeEvent<T, D>(queueName: QueueName, handler: (event: Event<T, D>) => Promise<void>) {
    await this.boss.work(queueName, queueOptions, async (event) => {
      try {
        await handler(event as unknown as Event<T, D>);
        return true;
      } catch (error) {
        this.logger.error('Job processing error:', error);
        return false;
      }
    });
  }

  async close() {
    this.logger.info('Initiating close of the job queue');
    await this.boss.stop();
  }
}