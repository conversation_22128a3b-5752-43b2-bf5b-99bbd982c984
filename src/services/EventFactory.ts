import { randomUUID } from 'crypto';
import { EventAction, EventType, JobEventData, Event } from '../types/event';


/**
 * EventFactory class for creating different types of events with consistent structure
 */
export class EventFactory {
  /**
   * Creates a generic event with automatically generated id and timestamp
   */
  static createEvent<T, D>(params: {
    type: T;
    action: EventAction;
    data: D;
  }): Event<T, D> {
    return {
      id: randomUUID(),
      version: 1.0,
      timestamp: new Date(),
      ...params
    };
  }

  /**
   * Creates a Job-specific event
   */
  static createJobEvent(action: EventAction, data: JobEventData): Event<EventType.JOB, JobEventData> {
    return this.createEvent({
      type: EventType.JOB,
      action,
      data
    });
  }
}