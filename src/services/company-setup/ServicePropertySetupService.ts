import { CreateServicePropertySetupDto, ResponseServicePropertySetupDto, UpdateServicePropertySetupDto } from './../../dto/v1/company-setup/ServicePropertySetupDto';
import { ServicePropertySetupBuilder } from "../../builders";
import { ServicePropertySetupRepository } from "../../db";
import { EntityNotFoundError } from 'typeorm';
import { HTTP_ERROR_TYPES } from '../../constants';
import { BaseError } from '../../errors';
import { JobType } from '../../types';

export class ServicePropertySetupService {

    constructor(
        private readonly servicePropertySetupRepository: ServicePropertySetupRepository = new ServicePropertySetupRepository(),
    ){}

    async findAllServicePropertySetupForCompany(companyId: number, offset: number, limit: number): Promise<{result: ResponseServicePropertySetupDto[], total: number}> {
        try{
            const [data, total] = await this.servicePropertySetupRepository.findAllServicePropertySetupForCompany(companyId, offset, limit);
            const servicePropertyDTOs = data.map((entity) => new ServicePropertySetupBuilder().fromEntity(entity).buildDto());
            return {
                result: servicePropertyDTOs,
                total
            }
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Service Property Setup with for company not found.`)
            }
        }
    }

    async findAllServicePropertySetupForCompanyWithoutLimit(companyId: number, type?: JobType, active?: boolean): Promise<ResponseServicePropertySetupDto[]>{
        try{
            const result = await this.servicePropertySetupRepository.findAllServicePropertySetupForCompanyWithoutLimit(companyId, type, active);
            return result.map((entity) => new ServicePropertySetupBuilder().fromEntity(entity).buildDto());
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Service Property Setup with for company not found.`)
            }
        }
    }

    async findServicePropertySetupByID(servicePropertySetupId: string, companyId: number): Promise<ResponseServicePropertySetupDto[]> {
        try{
            const result = await this.servicePropertySetupRepository.findServicePropertySetupByID(servicePropertySetupId, companyId);
            return Array.of(new ServicePropertySetupBuilder().fromEntity(result).buildDto());
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Service Property Setup with with id: ${servicePropertySetupId}, not found.`)
            }
        }
    }

    async createServicePropertySetup(dto: CreateServicePropertySetupDto,  companyId: number): Promise<ResponseServicePropertySetupDto[]> {
        const entity = await new ServicePropertySetupBuilder().fromDto(dto).setEntityCompanyId(companyId).buildEntity();
        const newEntity = await this.servicePropertySetupRepository.createServicePropertySetup(entity);
        return Array.of(new ServicePropertySetupBuilder().fromEntity(newEntity).buildDto());
    }

    async updateServicePropertySetupById(dto: UpdateServicePropertySetupDto, servicePropertySetupId: string, companyId: number): Promise<ResponseServicePropertySetupDto[]> {
        const entity = await new ServicePropertySetupBuilder().fromDto(dto).setEntityServicePropertyId(servicePropertySetupId).setEntityCompanyId(companyId).buildEntity();
        const updatedEntity = await this.servicePropertySetupRepository.updateServicePropertySetupById(entity);
        return Array.of(new ServicePropertySetupBuilder().fromEntity(updatedEntity).buildDto());
    }

    async deleteServicePropertySetupById(servicePropertySetupId: string, companyId: number){
        return await this.servicePropertySetupRepository.deleteServicePropertySetupById(servicePropertySetupId, companyId);
    }
}
