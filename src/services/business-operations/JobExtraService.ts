import _ from "lodash";
import { Logging } from "../../util";
import { BaseError } from "../../errors";
import { HTTP_ERROR_TYPES } from "../../constants";
import { CreateJobExtraDto, ResponseJobExtraDto, UpdateJobExtraDto } from "../../dto/v1";
import { JobExtraBuilder } from "../../builders";
import { EntityNotFoundError } from "typeorm";
import { JobExtraRepository } from "../../persistence";
import { JobType } from "../../types";

export class JobExtraService {

    constructor(
        private jobExtraRepository: JobExtraRepository = new JobExtraRepository()
    ){}

    async findResidentialCleaningExtrasPerCompanyId (companyId: number, offset: number, limit: number): Promise<{extras: ResponseJobExtraDto[], total: number}> {
        try{
            const [data, total] = await this.jobExtraRepository.findResidentialCleaningExtrasPerCompanyId(companyId, offset, limit);
            const resCleaningJobExtras: ResponseJobExtraDto[] = data.map((entity) => new JobExtraBuilder().fromEntity(entity).buildDto());
            return {
                extras: resCleaningJobExtras,
                total,
            }
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Job Extra with for company not found.`)
            }
        }
    }

    async findJobExtrasForCompanyWithoutLimit (companyId: number,type?: JobType, active?: boolean): Promise<ResponseJobExtraDto[]> {
        try{
            const result= await this.jobExtraRepository.findJobExtrasForCompanyWithoutLimit(companyId, type, active);
            return result.map((entity) => new JobExtraBuilder().fromEntity(entity).buildDto());
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Job Extra with for company not found.`)
            }
        }
    }

    async findResidentialCleaningExtraPerExtraId(id: string, companyId:number): Promise<ResponseJobExtraDto[]> {
        try{
            const entity = await this.jobExtraRepository.findResidentialCleaningExtraPerExtraId(id, companyId);
            return Array.of(new JobExtraBuilder().fromEntity(entity).buildDto());
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Job Extras with for company not found.`)
            }
        }
    }

    async createResidentialCleaningExtraPerCompanyId(dto: CreateJobExtraDto, companyId:number): Promise<ResponseJobExtraDto[]>{
        const entity = new JobExtraBuilder().fromDto(dto).setCompanyId(companyId).buildEntity();
        const newEntity = await this.jobExtraRepository.createResidentialCleaningExtrasPerCompanyId(entity);
        return Array.of(new JobExtraBuilder().fromEntity(newEntity).buildDto());

    }

    async updateResidentialCleaningExtraPerCompanyId(dto: UpdateJobExtraDto, id: string, companyId:number): Promise<ResponseJobExtraDto[]> {
        const entity = new JobExtraBuilder().fromDto(dto).setCompanyId(companyId).setExtraId(id).buildEntity();
        const updateEntity = await this.jobExtraRepository.updateResidentialCleaningExtrasPerCompanyId(entity);
        return Array.of(new JobExtraBuilder().fromEntity(updateEntity).buildDto());
    }

    async deleteResidentialCleaningExtrasPerCompanyId(id: string, companyId:number) {
        return await this.jobExtraRepository.deleteResidentialCleaningExtrasPerCompanyId(id, companyId);
    }
}