import { EntityNotFoundError } from "typeorm";
import { TypeOfServiceBuilder } from "../../builders";
import { HTTP_ERROR_TYPES } from "../../constants";
import { TypeOfServiceRepository } from "../../persistence";
import { CreateTypeOfServiceDto, ResponseTypeOfServiceDto, UpdateTypeOfServiceDto } from "../../dto/v1";
import { BaseError } from "../../errors";
import { JobType } from "../../types";

export class TypeOfServiceService {
    constructor(
        private readonly typeOfServiceRepository: TypeOfServiceRepository = new TypeOfServiceRepository(),    ){}

    async createNewTypeOfService(companyId: number, dto: CreateTypeOfServiceDto): Promise<ResponseTypeOfServiceDto[]>{
        const newTypeOfServiceEntity = new TypeOfServiceBuilder().fromDto(dto).setCompanyId(companyId).buildEntity();
        const newTypeOfService = await this.typeOfServiceRepository.createNewTypeOfServicePerCompanyId(newTypeOfServiceEntity);
        return Array.of(new TypeOfServiceBuilder().fromEntity(newTypeOfService).buildDto());
    }

    async updateTypeOfService(typeOfServiceId: string, dto: UpdateTypeOfServiceDto, companyId: number): Promise<ResponseTypeOfServiceDto[]>{
        const newTypeOfServiceEntity = new TypeOfServiceBuilder().fromDto(dto).setTypeOfServiceId(typeOfServiceId).setCompanyId(companyId).buildEntity();
        const updatedTypeOfServiceEntity = await this.typeOfServiceRepository.updateTypeOfServicePerId(newTypeOfServiceEntity);
        return Array.of(new TypeOfServiceBuilder().fromEntity(updatedTypeOfServiceEntity).buildDto()); 
    }

    async findAllTypeOfServicesForCompany(companyId: number, offset: number, limit: number): Promise<{result: ResponseTypeOfServiceDto[],total: number}>{
        try{
            const [data, total] = await this.typeOfServiceRepository.findAllTypeOfServicesForCompany(companyId, offset, limit);
            const typeOfServices: ResponseTypeOfServiceDto[] = data.map((typeOfService) => new TypeOfServiceBuilder().fromEntity(typeOfService).buildDto());
            return {
                result: typeOfServices,
                total
            }
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Type of Service with for company not found.`)
            }
        }
    }

    async findAllTypeOfServicesForCompanyWithoutLimit(companyId: number, type?: JobType, active?: boolean): Promise<ResponseTypeOfServiceDto[]>{
        try{
            const result = await this.typeOfServiceRepository.findAllTypeOfServicesForCompanyWithoutLimit(companyId, type, active);
            return result.map((typeOfService) => new TypeOfServiceBuilder().fromEntity(typeOfService).buildDto());
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Type of Service with for company not found.`)
            }
        }
    }

    async findTypeOfServiceByServiceId(typeOfServiceId: string, companyId: number):  Promise<ResponseTypeOfServiceDto[]>{
        try{
            const typeOfServiceEntity = await this.typeOfServiceRepository.findTypeOfServiceByServiceId(typeOfServiceId, companyId);
            return Array.of(new TypeOfServiceBuilder().fromEntity(typeOfServiceEntity).buildDto())
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Type of Service with with id: ${typeOfServiceId}, not found.`)
            }
        }
    }

    async deleteTypeOfServiceByServiceId(typeOfServiceId: string, companyId: number){
        return await this.typeOfServiceRepository.deleteTypeOfServiceByServiceId(typeOfServiceId, companyId);
    }
}