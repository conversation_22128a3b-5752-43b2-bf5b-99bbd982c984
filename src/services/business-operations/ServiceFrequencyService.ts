import { EntityNotFoundError } from "typeorm";
import { ServiceFrequencyBuilder } from "../../builders";
import { HTTP_ERROR_TYPES } from "../../constants";
import { ServiceFrequencyRepository } from "../../persistence";
import { CreateServiceFrequencyDto, ResponseServiceFrequencyDto, UpdateServiceFrequencyDto } from "../../dto/v1";
import { BaseError } from "../../errors";
import { JobType } from "../../types";

export class ServiceFrequencyService {

    constructor(
        private readonly serviceFrequencyRepository: ServiceFrequencyRepository = new ServiceFrequencyRepository()
    ){}

    async findServiceFrequencyForCompany({
        companyId, offset, limit, isActive=true
    }: {
        companyId: number, offset: number, limit: number, isActive?: boolean
    }): Promise<{result: ResponseServiceFrequencyDto[], total:number}>{
        try{
            const [data, total] = await this.serviceFrequencyRepository.findServiceFrequencyForCompany(companyId, offset, limit, isActive);
            const serviceFrequencies: ResponseServiceFrequencyDto[] = data.map((serviceArea) => new ServiceFrequencyBuilder().fromEntity(serviceArea).buildDto());
            return {
                result: serviceFrequencies,
                total
            }
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Service Frequencies with for company not found.`)
            }
        }
    }

    async findAllServiceFrequenciesForCompanyByJobType({
        companyId, type, isActive=true
    }: {
        companyId: number, type: JobType, isActive: boolean
    }): Promise<ResponseServiceFrequencyDto[]> {
        try{
            const result = await this.serviceFrequencyRepository.findServiceFrequencyForCompanyByJobType(companyId, type, isActive);
            return result.map((serviceFreq) => new ServiceFrequencyBuilder().fromEntity(serviceFreq).buildDto());
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Service Frequencies with for company not found.`)
            }
        }
    }

    async findServiceFrequencyForCompanyById({id, companyId, isActive = true}: {id: string, companyId: number, isActive?: boolean}): Promise<ResponseServiceFrequencyDto[]> {
        try{
            const entity = await this.serviceFrequencyRepository.findServiceFrequencyForCompanyById(id, companyId, isActive);
            return Array.of(new ServiceFrequencyBuilder().fromEntity(entity).buildDto());
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Service Frequencies with id${id} not found.`)
            }
        }
    }

    async createServiceFrequencyForCompany(dto: CreateServiceFrequencyDto, companyId: number): Promise<ResponseServiceFrequencyDto[]> {
        const entity = new ServiceFrequencyBuilder().fromDto(dto).setEntityCompanyId(companyId).buildEntity();
        const newEntity = await this.serviceFrequencyRepository.createServiceFrequencyForCompany(entity);
        return Array.of(new ServiceFrequencyBuilder().fromEntity(newEntity).buildDto())
    }

    async updateServiceFrequencyForCompany(dto: Partial<UpdateServiceFrequencyDto>, id: string, companyId: number): Promise<ResponseServiceFrequencyDto[]> {
        const entity = new ServiceFrequencyBuilder().fromDto(dto).setEntitySubscriptionTypeId(id).setEntityCompanyId(companyId).buildEntity();
        const updatedEntity = await this.serviceFrequencyRepository.updateServiceFrequencyForCompany(entity);
        return Array.of(new ServiceFrequencyBuilder().fromEntity(updatedEntity).buildDto());
    }

    async deleteServiceFrequencyForCompany(id: string, companyId: number){
        return await this.serviceFrequencyRepository.deleteServiceFrequencyForCompany(id, companyId);
    }

}