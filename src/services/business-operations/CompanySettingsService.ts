import { EntityNotFoundError } from "typeorm";
import { CompanySettingsBuilder } from "../../builders";
import { CompanySettingsRepository } from "../../persistence";
import { ResponseCompanySettingsDto, UpsertCompanySettingsDto } from "../../dto/v1";
import { BaseError } from "../../errors";
import { HTTP_ERROR_TYPES } from "../../constants";
import { Logging } from "../../util";

export class CompanySettingsService {
    constructor(
        private readonly companySettingsRepository: CompanySettingsRepository,
        private readonly logger: Logging
    ){}

    public async retrieveCompanySettingsById(id: string, companyId: number): Promise<ResponseCompanySettingsDto> {
        try{
            const result = await this.companySettingsRepository.retrieveCompanySettingsById(id, companyId);
            return new CompanySettingsBuilder().fromEntity(result).buildDto();
        }catch(error){
            this.logger.error(`Failed to retrieve company settings with following message: ${error.message}`);
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Company settings with with id: ${id}, not found.`)
            }
        }
    }

    public async createCompanySettings(dto: UpsertCompanySettingsDto, companyId: number) {
        const entity = new CompanySettingsBuilder().fromDto(dto).setCompanyId(companyId).buildEntity();
        const result = await this.companySettingsRepository.upsertCompanySettings(entity);
        return new CompanySettingsBuilder().fromEntity(result).buildDto();
    }


    public async updateCompanySettings(dto: UpsertCompanySettingsDto, id: string, companyId: number) {
        const entity = new CompanySettingsBuilder().fromDto(dto).setId(id).setCompanyId(companyId).buildEntity();
        const result = await this.companySettingsRepository.upsertCompanySettings(entity);
        return new CompanySettingsBuilder().fromEntity(result).buildDto();
    }

    public async deleteCompanySettingsById(id: string, companyId: number) {
        return await this.companySettingsRepository.deleteCompanySettingsById(id, companyId);
    }
}