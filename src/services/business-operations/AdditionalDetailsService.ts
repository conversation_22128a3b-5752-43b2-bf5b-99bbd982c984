import { EntityNotFoundError } from 'typeorm';
import { AdditionalDetailsBuilder } from '../../builders';
import { HTTP_ERROR_TYPES } from '../../constants';
import { CreateAdditionalDetailsDto, ResponseAdditionalDetailsDto, UpdateAdditionalDetailsDto } from '../../dto/v1';
import { BaseError } from '../../errors';
import { JobType } from '../../types';
import { AdditionalDetailsRepository } from '../../persistence';
export class AdditionalDetailsService {

    constructor(
        private readonly additionalDetailsRepository: AdditionalDetailsRepository = new AdditionalDetailsRepository(),
        private readonly additionalDetailsBuilder: AdditionalDetailsBuilder = new AdditionalDetailsBuilder()
    ){}

    async findAllAdditionalDetailsForCompany(companyId: number, offset: number, limit: number): Promise<{result: ResponseAdditionalDetailsDto[], total: number}>{
        const [data, total] = await this.additionalDetailsRepository.findAllAdditionalDetailsForCompany(companyId, offset, limit);
        const additionalDetails: ResponseAdditionalDetailsDto[] = data.map((additionalDetail) => new AdditionalDetailsBuilder().fromEntity(additionalDetail).buildDto());
        return {
            result: additionalDetails,
            total
        }
    }


    async findAllAdditionalDetailsForCompanyNoLimit(companyId: number, type?: JobType, active?: boolean): Promise<ResponseAdditionalDetailsDto[]>{
        const entities = await this.additionalDetailsRepository.findAllAdditionalDetailsForCompanyNoLimit(companyId, type, active);
        return entities.map((additionalDetail) => new AdditionalDetailsBuilder().fromEntity(additionalDetail).buildDto());
    }

    async findAdditionalDetailForCompanyPerId(additionalDetailId: string, companyId: number): Promise<ResponseAdditionalDetailsDto[]> {
        try{
            const entity = await this.additionalDetailsRepository.findAdditionalDetailForCompanyPerId(additionalDetailId, companyId);
            return Array.of(new AdditionalDetailsBuilder().fromEntity(entity).buildDto())
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Additional Details with with id: ${additionalDetailId}, not found.`)
            }
        }
    }

    async createAdditionalDetailForCompany(dto: CreateAdditionalDetailsDto, companyId: number): Promise<ResponseAdditionalDetailsDto[]>{
        const entity = new AdditionalDetailsBuilder().fromDto(dto).setCompanyId(companyId).buildEntity();
        const newEntity = await this.additionalDetailsRepository.createAdditionalDetailForCompany(entity);
        return Array.of(new AdditionalDetailsBuilder().fromEntity(newEntity).buildDto());
    }

    async updateAdditionalDetailForCompanyPerId(dto: UpdateAdditionalDetailsDto, additionalDetailId: string, companyId: number): Promise<ResponseAdditionalDetailsDto[]>{
        const entity = new AdditionalDetailsBuilder().fromDto(dto).setAdditionalDetailId(additionalDetailId).setCompanyId(companyId).buildEntity();
        const updatedEntity = await  this.additionalDetailsRepository.updateAdditionalDetailForCompanyPerId(entity);
        return Array.of(new AdditionalDetailsBuilder().fromEntity(updatedEntity).buildDto());
    }

    async deleteAdditionalDetailForCompanyPerId(additionalDetailId: string, companyId: number){
        return await this.additionalDetailsRepository.deleteAdditionalDetailForCompanyPerId(additionalDetailId,companyId);
    }
}