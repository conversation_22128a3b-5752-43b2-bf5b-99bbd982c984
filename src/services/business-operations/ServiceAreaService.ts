import { EntityNotFoundError } from "typeorm";
import { ServiceAreaBuilder } from "../../builders";
import { HTTP_ERROR_TYPES } from "../../constants";
import { ServiceAreaRepository } from "../../persistence";
import { CreateServiceAreaDto, ResponseServiceAreaDto, UpdateServiceAreaDto } from "../../dto/v1";
import { BaseError } from "../../errors";
import { JobType } from "../../types";


export class ServiceAreaService {
    constructor(
        private readonly serviceAreaRepository: ServiceAreaRepository = new ServiceAreaRepository(),
    ){}

    async findAllServiceAreasByCompanyId(companyId: number, offset: number, limit: number): Promise<{result: ResponseServiceAreaDto[],total: number}> {
        try{
            const [data, total] = await this.serviceAreaRepository.findAllServiceAreasByCompanyId(companyId, offset, limit);
            const serviceAreas: ResponseServiceAreaDto[] = data.map((serviceArea) => new ServiceAreaBuilder().fromEntity(serviceArea).buildDto());
            return {
                result: serviceAreas,
                total
            }
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Service Area with for company not found.`)
            }
            throw error;
        }
    }

    async findAllServiceAreasByCompanyIdWithoutLimit(companyId: number, type?: JobType, active?: boolean): Promise<ResponseServiceAreaDto[]> {
        try{
            const result = await this.serviceAreaRepository.findAllServiceAreasByCompanyIdWithoutLimit(companyId, type, active);
            return result.map((serviceArea) => new ServiceAreaBuilder().fromEntity(serviceArea).buildDto());
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Service Area with for company not found.`)
            }
            throw error;
        }
    }

    async findServiceAreaById(serviceAreaId: string, companyId: number): Promise<ResponseServiceAreaDto[]>{
        try{
            const entity = await this.serviceAreaRepository.findServiceAreaById(serviceAreaId, companyId);
            return Array.of(new ServiceAreaBuilder().fromEntity(entity).buildDto());;
        }
        catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Service Area with with id: ${serviceAreaId}, not found.`)
            }
            throw error;
        }
    }

    async createServiceArea(dto: CreateServiceAreaDto, companyId: number): Promise<ResponseServiceAreaDto[]> {
        const entity = new ServiceAreaBuilder().fromDto(dto).setCompanyId(companyId).buildEntity();
        const newEntity = await this.serviceAreaRepository.createServiceArea(entity);
        return Array.of(new ServiceAreaBuilder().fromEntity(newEntity).buildDto());
    }

    async updateServiceAreaById(dto: UpdateServiceAreaDto, serviceAreaId: string, companyId: number): Promise<ResponseServiceAreaDto[]> {
        const entity = new ServiceAreaBuilder().fromDto(dto).setServiceAreaId(serviceAreaId).setCompanyId(companyId).buildEntity();
        const updateEntity = await this.serviceAreaRepository.updateServiceAreaById(entity);
        return Array.of(new ServiceAreaBuilder().fromEntity(updateEntity).buildDto());
    }

    async deleteServiceAreaById(serviceAreaId: string, companyId: number) {
        return await this.serviceAreaRepository.deleteServiceAreaById(serviceAreaId, companyId);
    }
}