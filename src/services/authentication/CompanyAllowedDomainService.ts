import { EntityNotFoundError } from "typeorm";
import { HTTP_ERROR_TYPES } from "../../constants";
import { CompanyAllowedDomainRepository } from "../../db";
import { BaseError } from "../../errors";

export class CompanyAllowedDomainService{

    constructor(
        private readonly companyAllowedDomainRepository: CompanyAllowedDomainRepository = new CompanyAllowedDomainRepository()
    ){}

    async findPartnerIdByKeyAndDomain(apiKey: string, domain: string): Promise<number> {
        try{
            const partner = await this.companyAllowedDomainRepository.findPartnerIdByKeyAndDomain(apiKey, domain); 
            return partner.company_id;
        }catch(error){
            if(error instanceof EntityNotFoundError){
                throw new BaseError(HTTP_ERROR_TYPES.UNAUTHORIZED_ORIGIN, 'Unknown Domain and Api Key.')
            }
            throw error;
        }
    }
}