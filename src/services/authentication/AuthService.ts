import bcrypt from 'bcryptjs';
import { AuthPersistance, ClientProfileRepository, EmployeeProfileRepository} from "../../db";
import { AuthenticationError, BaseError } from "../../errors";
import { Logging, generateTokens, verifyRefreshToken } from "../../util";
import { UserAuthEntity, UserCompanyAssociationEntity } from "../../entity/v1";
import { HTTP_ERROR_TYPES } from '../../constants';
import { UserLoginDto, UserTokenDto } from '../../dto/v1';
import AdminProfileRepository from '../../db/profile/AdminProfileRepository';
export default class AuthService  {

    constructor(
        private readonly logger: Logging,
        private readonly authPersistance = new AuthPersistance(logger),
        private readonly adminProfilePersistance = new AdminProfileRepository(logger),
        private readonly clientProfilePersistance = new ClientProfileRepository(logger),
        private readonly employeeProfilePersistance = new EmployeeProfileRepository(logger),
    ){}

    public async loginUser(dto: UserLoginDto): Promise<UserTokenDto> {
        const { username, password } = dto;

        const user: UserAuthEntity = await this.authPersistance.findUserCredentialsByUsername(username)
        .then(async (user) => await this.__checkUserNameNonExistOrPasswordMatch(user, password))

        if(user.role === 'admin'){
            const profile = await this.adminProfilePersistance.findAdminProfileByUserId(user.user_id);
            return await this.generateTokens(user, profile.company_info.company_id);
        } else if (user.role === 'employee'){
            const profile = await this.employeeProfilePersistance.findEmployeeProfileByUserId(user.user_id);
            return await this.generateTokens(user, profile.company_info.company_id);
        } else if (user.role === 'client'){
            const profile = await this.clientProfilePersistance.findClientProfileByUserId(user.user_id);
            return await this.generateTokens(user, profile.related_company_id);
        } else {
            throw new BaseError(HTTP_ERROR_TYPES.INVALID_USERNAME_PASSWORD_ERROR, 'Wrong user role.')
        }
    }

    public async validateRefreshToken(currentRefreshToken: string) {
        const decodedRefreshToken = verifyRefreshToken(currentRefreshToken);

        if(!decodedRefreshToken) {
            throw new AuthenticationError({
                name: HTTP_ERROR_TYPES.REFRESH_TOKEN_MALFORMED,
                message: "Refresh token is malformed." 
            });
        }
        const userCompanyAssociation: UserCompanyAssociationEntity = await this.authPersistance.findUserAssociationByUserId(decodedRefreshToken.userId);

        const user: UserAuthEntity = {
            user_id: userCompanyAssociation.userId,
            role: userCompanyAssociation.role,
        }

        return await this.generateTokens(user, userCompanyAssociation.companyId);

    }

    public async generateTokens(user: UserAuthEntity, companyId?: number) {
        const {accessToken, refreshToken} = await generateTokens(user, companyId);

        await this.authPersistance.createRefreshTokenRecord(refreshToken)
        return {accessToken, refreshToken}
    }

    private async __checkUserNameNonExistOrPasswordMatch(user: UserAuthEntity, password: string) {
        const passwordMatch = await bcrypt.compare(password, user.password);
        if (!user || !user.username || !passwordMatch) {
            throw new AuthenticationError({
                name: HTTP_ERROR_TYPES.INVALID_USERNAME_PASSWORD_ERROR,
                message: "Invalid username or password" 
            });
        }

        return user;
    }

}