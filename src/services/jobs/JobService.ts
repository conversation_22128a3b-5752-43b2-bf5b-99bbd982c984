import { AdditionalDetailsRepository } from './../../db/company-setup/AdditionalDetailsRepository';
import { JobExtraRepository } from './../../db/company-setup/JobExtraRepository';
import { ServicePropertySetupRepository } from './../../db/company-setup/ServicePropertySetupRepository';
import { ServiceFrequencyRepository } from './../../db/company-setup/ServiceFrequencyRepository';
import { JobBuilder, ScheduledJobDetailsBuilder } from "../../builders";
import { HTTP_ERROR_TYPES } from "../../constants";
import { EmployeeProfileRepository,  JobRepository, ServiceAreaRepository, TypeOfServiceRepository } from "../../db";
import { CreateResidentialCleaningJobDto, EmployeeProfileDto, ResponseScheduledJobDto, UpdateResidentialCleaningJobDto } from "../../dto/v1";
import { EmployeeProfileEntity, JobEntity, ScheduledJobDetailsEntity } from "../../entity/v1";
import { BaseError } from "../../errors";
import { PriceService } from "./PriceService";
import { RRule, Frequency} from 'rrule';
import { EntityNotFoundError, QueryFailedError } from "typeorm";

/**
 * Service responsible for managing residential cleaning jobs.
 * Handles job creation, scheduling, updating, and deletion operations.
 * Provides functionality for generating future scheduled jobs based on frequency settings.
 */
export class JobService {
    constructor(
        private readonly logger,
        private readonly priceService: PriceService,
        private readonly jobRepository: JobRepository,
        private readonly employeeProfileRepository: EmployeeProfileRepository = new EmployeeProfileRepository(logger),
        private readonly serviceFrequencyRepository: ServiceFrequencyRepository = new ServiceFrequencyRepository(),
        private readonly serviceTypeRepository: TypeOfServiceRepository = new TypeOfServiceRepository(),
        private readonly serviceAreaRepository: ServiceAreaRepository = new ServiceAreaRepository(),
        private readonly servicePropertySetupRepository: ServicePropertySetupRepository = new ServicePropertySetupRepository(),
        private readonly jobExtraRepository: JobExtraRepository = new JobExtraRepository(),
        private readonly additionalDetailsRepository: AdditionalDetailsRepository = new AdditionalDetailsRepository(),
    ){}

    async findScheduledJobsForCompanyByPage(
        companyId: number, 
        offset: number, 
        limit: number,
        startDate: string, 
        endDate: string
    ): Promise<{result: ResponseScheduledJobDto[], total: number}> {

        const startDateObj = this.parseDate(startDate, 'startDate');
        const endDateObj = this.parseDate(endDate, 'endDate');

        const [data, total] = await this.jobRepository.findScheduledJobsForCompanyByPage(companyId, offset, limit, startDateObj, endDateObj);
        const result = data.flatMap((scheduledJob) => this.createJobResponse({scheduledJob}));
        return {
            result,
            total
        }
    }

    async findScheduledJobsByDateRange (
        companyId: number, 
        startDate: string, 
        endDate: string
    ): Promise<ResponseScheduledJobDto[]> {
        const startDateObj = this.parseDate(startDate, 'startDate');
        const endDateObj = this.parseDate(endDate, 'endDate');

        const scheduledJobs = await this.jobRepository.findScheduledJobsByDateRange(companyId, startDateObj, endDateObj);
        return scheduledJobs.flatMap((scheduledJob) => this.createJobResponse({scheduledJob}));
    }

    async findScheduledJobForCompanyById (jobId: string, companyId: number): Promise<ResponseScheduledJobDto[]> {
        const scheduledJob = await this.jobRepository.findScheduledJobForCompanyById(jobId, companyId);
        return [this.createJobResponse({scheduledJob})];
    }

    async findScheduledJobForCompanyByClientId (clientId: number, companyId: number): Promise<ResponseScheduledJobDto[]> {
        try{
            const jobsEntities = await this.jobRepository.findScheduledJobsForClientAndCompany(clientId, companyId);
            return jobsEntities.flatMap((scheduledJob) =>  this.createJobResponse({scheduledJob}));
        }catch(error){
            throw new BaseError(HTTP_ERROR_TYPES.CLIENT_DATA_ERROR, `Could not find any jobs for client: ${clientId}.`);
        }

    }

    /**
     * Finds jobs by pagination offset with a specified batch size.
     *
     * @param offset - The number of records to skip
     * @param batchSize - The number of records to return
     * @param numMonths - The number of months to look ahead for jobs
     * @returns Promise resolving to an array of JobEntity objects
     */
    async findJobsByOffset(offset: number, batchSize: number, numMonths: number): Promise<JobEntity[]> {
        return this.jobRepository.findJobsForBatchSize(offset, batchSize, numMonths);
    }

    /**
     * Finds the last scheduled job by job ID.
     * Handles cases where no scheduled job exists for the given job ID.
     *
     * @param id - The ID of the job to find
     * @returns Promise resolving to a ScheduledJobDetailsEntity or null if none exists
     */
    async findLastScheduledJobById(id: string): Promise<ScheduledJobDetailsEntity | null> {
        return await this.jobRepository.findLastScheduledJobById(id);
    }

    /**
     * Finds the last scheduled jobs for multiple job IDs.
     *
     * @param ids - Array of job IDs to find last scheduled jobs for
     * @returns Promise resolving to an array of ScheduledJobDetailsEntity objects
     */
    async findLastScheduledJobsByIds(ids: string[]): Promise<ScheduledJobDetailsEntity[]> {
        try{
            return await this.jobRepository.findLastScheduledJobsByIds(ids);
        }catch(error){
            throw new BaseError(HTTP_ERROR_TYPES.UNKNOwN_ERROR, `Unknown error during job creation.`);
        }
    }

    /**
     * Creates a new residential cleaning job for a specific company.
     *
     * @param dto - Data transfer object containing job details
     * @param companyId - ID of the company the job belongs to
     * @returns Promise resolving to an array of ResponseScheduledJobDto objects
     * @throws BaseError if job creation fails
     */
    async createJobByCompanyId(dto: CreateResidentialCleaningJobDto, companyId: number): Promise<ResponseScheduledJobDto[]> {
        try{
            // TODO: Remove the call to employees by making the entity better.
            // const employeeProfileEntities = await this.findEmployeeProfilesByIds(dto.assignedEmployees);

            const [serviceFrequency, serviceArea, serviceType, serviceDetails, extras, additionalDetails] = await this.findAllPricesForOptions(
                dto.serviceFrequency,
                dto.serviceArea,
                dto.serviceType,
                dto.serviceDetails?.map(scheduledJob => scheduledJob.id),
                dto.extras?.map(extra => extra.id),
                dto.additionalDetails?.map(additionalDetail => additionalDetail.choiceId),
                companyId
            );

            const totalPrice = this.calculateJobPrice(
                serviceType.price,
                serviceArea.price,
                this.matchOptionAmountWithPrice(dto.serviceDetails, serviceDetails),
                this.matchOptionAmountWithPrice(dto.extras, extras),
                serviceFrequency.price,
                serviceFrequency.discount,
                additionalDetails.map(detail => detail.price)
            );

            const jobEntity: JobEntity = new JobBuilder()
            .setEntityCompanyId(companyId)
            .setEntityType(dto.type)
            .setLastJobGeneratedDatetime(new Date())
            .setServiceFrequencyEntity(serviceFrequency)
            .fromDto(dto)
            .buildEntity()

            // TODO: Possibly rethink the relationship between JobEntity and Scheduled Job.
            const scheduledJobDetail = new ScheduledJobDetailsBuilder()
            .setEntityCompanyId(companyId)
            .setScheduledJobPrice(totalPrice)
            .setJobInfoEntity(jobEntity)
            // .setEntityEmployeeProfiles(employeeProfileEntities)
            .fromDto(dto)
            .buildEntity()

            // Create new job specific logic must remain in this method.
            const futureScheduledJobs = this.generateScheduledJobs(jobEntity, scheduledJobDetail, 3);
            futureScheduledJobs.push(scheduledJobDetail);

            jobEntity.scheduled_jobs = futureScheduledJobs;

            const newJobEntity = await this.jobRepository.createJobEntityForCompanyById(jobEntity);

            return [this.createJobResponse({scheduledJob: newJobEntity.scheduled_jobs.pop(), jobInfo: newJobEntity})];
        }catch(error){
            if (error instanceof QueryFailedError) {
                this.logger.error(`[QueryFailedError] ${error.message}`)
            }

            if (error instanceof EntityNotFoundError) {
                this.logger.error(`[EntityNotFoundError] ${error.message}`)
            }

            throw new BaseError(HTTP_ERROR_TYPES.UNKNOwN_ERROR, `Unknown error during job creation.`);
        }
    }

    /**
     * Creates a batch of scheduled jobs.
     *
     * @param scheduledJobsEntities - Array of ScheduledJobDetailsEntity objects to create
     * @returns Promise that resolves when the batch creation is complete
     */
    async createScheduledJobsBatch(scheduledJobsEntities: ScheduledJobDetailsEntity[]): Promise<void> {
        await this.jobRepository.createScheduledJobsBatch(scheduledJobsEntities);
    }

    /**
     * Generates scheduled jobs based on a job entity and frequency settings.
     * Handles cases where no previous scheduled job exists.
     *
     * @param job - The job entity to generate scheduled jobs for
     * @param lastScheduledJob - The last scheduled job, used as a reference for timing, or null if none exists
     * @param numMonths - Number of months to generate jobs for (default: 3)
     * @returns Array of ScheduledJobDetailsEntity objects representing future scheduled jobs
     * @throws Error if unable to generate scheduled jobs
     */
    public generateScheduledJobs(job: JobEntity, lastScheduledJob: ScheduledJobDetailsEntity | null = null, numMonths: number = 3): ScheduledJobDetailsEntity[] {
        try {
            this.logger.info(`Generating scheduled jobs for job ID: ${job.id}, using last scheduled job: ${lastScheduledJob ? lastScheduledJob.id : 'none'}`);

            // Get all occurrence end times until the horizon end date
            const allFutureEndDates = this.createJobsFutureDates(job, lastScheduledJob, numMonths);

            if (allFutureEndDates.length === 0) {
                this.logger.warn(`No future dates generated for job ID: ${job.id}`);
                return [];
            }

            const scheduledJobsEntities: ScheduledJobDetailsEntity[] = [];

            // If we don't have a reference scheduled job, we can't create new ones
            if (!lastScheduledJob) {
                this.logger.warn(`No reference scheduled job available for job ID: ${job.id}`);
                return [];
            }

            // Generate scheduled jobs for each occurrence
            for (const futureEndDate of allFutureEndDates) {
                try {
                    // Calculate the startTime by subtracting the job duration
                    const {startTime, endTime} = this.createFutureJobStartAndEndTime(lastScheduledJob, futureEndDate);

                    const futureScheduledJob = this.createScheduleJob(lastScheduledJob, startTime, endTime);

                    scheduledJobsEntities.push(futureScheduledJob);
                } catch (error) {
                    this.logger.error(`Error creating scheduled job for date ${futureEndDate}: ${error.message}`);
                    // Continue with other dates
                }
            }

            return scheduledJobsEntities;
        } catch (error) {
            this.logger.error(`Error generating scheduled jobs for job ID ${job.id}: ${error.message}`);
            return [];
        }
    }

    /**
     * Creates future dates for job scheduling based on frequency settings.
     * Handles cases where no last scheduled job exists.
     *
     * @param job - The job entity containing frequency information
     * @param lastScheduledJob - The last scheduled job to use as a reference, or null if none exists
     * @param numMonths - Number of months to look ahead
     * @returns Array of Date objects representing future job dates
     * @private
     */
    private createJobsFutureDates(job: JobEntity, lastScheduledJob: ScheduledJobDetailsEntity | null, numMonths: number): Date[] {
        // Use current date if no last scheduled job exists
        const baseDate: Date = lastScheduledJob ? new Date(lastScheduledJob.end_time) : new Date();

        // Calculate horizon end date (numMonths from base date)
        const futureEndDate: Date = new Date(baseDate);
        futureEndDate.setMonth(baseDate.getMonth() + numMonths);

        // Set start date for recurrence rule
        const startDate = lastScheduledJob ? new Date(lastScheduledJob.start_time) : baseDate;

        // Parse the RRule from the job entity
        const rule = new RRule({
            freq: this.getRRuleFrequencyEnum(job.service_frequency.frequency_name),
            count: job.service_frequency.frequency_count || null,
            interval: job.service_frequency.frequency_interval,
            dtstart: startDate, // Use appropriate start date
        });

        return rule.between(baseDate, futureEndDate, true);
    }

    /**
     * Creates start and end times for a future job based on a reference job and a future end date.
     * Handles cases where the reference job might be null.
     *
     * @param scheduledJob - The reference scheduled job to extract time information from, or null
     * @param futureEndDate - The future date to use as a base
     * @returns Object containing startTime and endTime Date objects
     * @private
     */
    private createFutureJobStartAndEndTime(scheduledJob: ScheduledJobDetailsEntity | null, futureEndDate: Date): {startTime: Date, endTime: Date} {
        // Extract reference hours/minutes/seconds from the job entity
        let startHours = 0, startMinutes = 0, startSeconds = 0; // Default to 00:00 AM
        let endHours = 2, endMinutes = 0, endSeconds = 0;     // Default to 02:00 AM (2-hour job)

        // Only try to extract times if we have a scheduled job
        if (scheduledJob) {
            const startDateTime = new Date(scheduledJob.start_time);
            const endDateTime = new Date(scheduledJob.end_time);

            if (startDateTime && endDateTime) {
                // Use the provided times for reference
                startHours = startDateTime.getHours();
                startMinutes = startDateTime.getMinutes();
                startSeconds = startDateTime.getSeconds();

                endHours = endDateTime.getHours();
                endMinutes = endDateTime.getMinutes();
                endSeconds = endDateTime.getSeconds();
            }
        }

        const endTime = new Date(futureEndDate);
        const startTime = new Date(futureEndDate);

        // Set the appropriate times
        startTime.setHours(startHours, startMinutes, startSeconds);
        endTime.setHours(endHours, endMinutes, endSeconds);

        return {
            startTime,
            endTime
        }
    }

    /**
     * Creates a new scheduled job entity based on a reference job and new start/end times.
     * Handles cases where the reference job might be null.
     *
     * @param scheduledJob - The reference scheduled job to copy properties from, or null
     * @param startTime - The start time for the new scheduled job
     * @param endTime - The end time for the new scheduled job
     * @returns A new ScheduledJobDetailsEntity with updated times
     * @throws Error if scheduledJob is null and no default properties are available
     * @private
     */
    private createScheduleJob(scheduledJob: ScheduledJobDetailsEntity | null, startTime: Date, endTime: Date): ScheduledJobDetailsEntity {
        if (!scheduledJob) {
            throw new Error('Cannot create a scheduled job without a reference job');
        }

        const { start_time, end_time, id, ...otherProps } = scheduledJob;

        return {
            id: undefined, // For new record
            start_time: startTime,
            end_time: endTime,
            ...otherProps
        };
    }

    /**
     * Converts a string frequency name to the corresponding RRule Frequency enum value.
     *
     * @param frequencyName - The frequency name as a string (DAILY, WEEKLY, MONTHLY)
     * @returns The corresponding Frequency enum value
     * @throws BaseError if the frequency name is not supported
     * @private
     */
    private getRRuleFrequencyEnum(frequencyName: string): Frequency {
        switch (frequencyName) {
          case 'DAILY': // daily
            return Frequency.DAILY;
          case 'WEEKLY': // weekly
            return Frequency.WEEKLY;
          case 'MONTHLY': // monthly
            return Frequency.MONTHLY;
          default:
            throw new BaseError(HTTP_ERROR_TYPES.UNKNOwN_ERROR, `Unsupported frequency_count: ${frequencyName}`);
        }
    }

    /**
     * Updates a scheduled job by ID.
     *
     * @param dto - Data transfer object containing updated job details
     * @param id - ID of the scheduled job to update
     * @param companyId - ID of the company the job belongs to
     * @returns Promise resolving to an array containing the updated job response
     * @throws BaseError if the job is not found or update fails
     */
    public async updateScheduledJobById (dto: UpdateResidentialCleaningJobDto, id: string, companyId: number) {
        try{
            const currentScheduledJob = await this.jobRepository.findScheduledJobForCompanyById(id, companyId);

            const needPriceCalculation = this.needPriceCalculation(dto);

            const jobEntity: JobEntity = new JobBuilder()
            .setEntityCompanyId(companyId)
            .setEntityType(dto.type)
            .setOldJobInfoOptions(currentScheduledJob.job_info)
            .fromDto(dto)
            .buildEntity();

            //It is important to set currentJob data before mapping new dto data.
            const scheduledJob = new ScheduledJobDetailsBuilder()
            .setEntityCompanyId(companyId)
            .setOldScheduledJobOptions(currentScheduledJob)
            .setJobInfoEntity(jobEntity)
            .fromDto(dto)
            .buildEntity();

            if (needPriceCalculation) {
                const [serviceFrequency, serviceArea, serviceType, serviceDetails, extras, additionalDetails] = await this.findAllPricesForOptions(
                    scheduledJob.job_info.related_service_frequency_id,
                    scheduledJob.related_service_area_id,
                    scheduledJob.related_service_type_id,
                    scheduledJob.service_details.map(scheduledJob => scheduledJob.related_property_setup_id),
                    scheduledJob.extras.map(extra => extra.related_extra_id),
                    scheduledJob.additionalDetails.map(additionalDetail => additionalDetail.related_additional_details_choice_id),
                    companyId
                );

                const totalPrice = this.calculateJobPrice(serviceType.price,
                    serviceArea.price,
                    this.matchOptionAmountWithPrice(
                        scheduledJob.service_details.map(detail => ({
                          id: detail.related_property_setup_id,
                          amount: detail.amount
                        })),
                        serviceDetails
                    ),
                    this.matchOptionAmountWithPrice(
                        scheduledJob.extras.map(extra => ({
                            id: extra.related_extra_id,
                            amount: extra.amount
                        })),
                        extras
                    ),
                    serviceFrequency.price,
                    serviceFrequency.discount,
                    additionalDetails.map(detail => detail.price));

                scheduledJob.total_price = totalPrice;
            }

            const updatedJob = await this.jobRepository.updateResidentialCleaningJobByJobId(scheduledJob);

            return [this.createJobResponse({scheduledJob:updatedJob})];
        }catch(error){
            if (error instanceof QueryFailedError) {
                this.logger.error(`[QueryFailedError] ${error.message}`)
            }

            if (error instanceof EntityNotFoundError) {
                this.logger.error(`[EntityNotFoundError] ${error.message}`)
            }

            throw new BaseError(HTTP_ERROR_TYPES.DATA_NOT_FOUND, `Job for jobId ${id} not found`);
        }
    }

    /**
     * Updates the last generated time for a job.
     *
     * @param job - The job entity to update
     * @returns Promise that resolves when the update is complete
     */
    public async updateJobInfoLastGeneratedTimeById(job: JobEntity): Promise<void> {
        await this.jobRepository.updateJobInfoLastGeneratedTimeById(job);
    }

    /**
     * Updates the last generated time for multiple jobs in a batch.
     *
     * @param jobs - Array of job entities to update
     * @returns Promise that resolves when the batch update is complete
     */
    public async updateJobInfoLastGeneratedTimeByIdBatch(jobs: JobEntity[]): Promise<void> {
        await this.jobRepository.updateJobInfoLastGeneratedTimeByIdBatch(jobs);
    }

    /**
     * Deletes a scheduled job by ID.
     *
     * @param id - ID of the scheduled job to delete
     * @param companyId - ID of the company the job belongs to
     * @returns Promise resolving to the result of the deletion operation
     */
    public async cancelScheduledJobByJobId(id: string, companyId: number) {
        return await this.jobRepository.cancelScheduledJobByJobId(id, companyId);
    }

    /**
     * Deletes a job info record by ID.
     *
     * @param id - ID of the job info to delete
     * @param companyId - ID of the company the job belongs to
     * @returns Promise resolving to the result of the deletion operation
     */
    public async deleteJobPermanentlyByJobId(id: string, companyId: number) {
        return await this.jobRepository.deleteJobPermanentlyByJobId(id, companyId);
    }

    /**
     * Finds employee profiles by their IDs.
     *
     * @param assignedEmployees - Array of employee profile DTOs containing profile IDs
     * @returns Promise resolving to an array of EmployeeProfileEntity objects or null
     * @private
     */
    private async findEmployeeProfilesByIds(assignedEmployees: EmployeeProfileDto[]):Promise<EmployeeProfileEntity[]| null> {
        if(assignedEmployees){
            return await this.employeeProfileRepository.findEmployeeProfilesByIds(assignedEmployees.map((employeeDto) => employeeDto.profileId));
        }
        return;
    }

    /**
     * Finds all price-related information for job options.
     *
     * @param serviceFrequencyId - ID of the service frequency
     * @param serviceAreaId - ID of the service area
     * @param serviceTypeId - ID of the service type
     * @param serviceDetailIds - Array of service detail IDs
     * @param extraIds - Array of extra service IDs
     * @param additionalDetailIds - Array of additional detail IDs
     * @param companyId - ID of the company
     * @returns Promise resolving to an array containing all price-related entities
     * @private
     */
    private async findAllPricesForOptions(
        serviceFrequencyId: string,
        serviceAreaId: string,
        serviceTypeId: string,
        serviceDetailIds: string[] = [],
        extraIds: string[] = [],
        additionalDetailIds: string[] = [],
        companyId: number
    ): Promise<any> {
        return await Promise.all([
            this.serviceFrequencyRepository.findServiceFrequencyForCompanyById(serviceFrequencyId, companyId),
            this.serviceAreaRepository.findServiceAreaPriceById(serviceAreaId, companyId),
            this.serviceTypeRepository.findServiceTypePriceById(serviceTypeId, companyId),
            this.servicePropertySetupRepository.findServicePropertySetupPriceById(serviceDetailIds, companyId),
            this.jobExtraRepository.findJobExtrasPriceById(extraIds, companyId),
            this.additionalDetailsRepository.findAdditionalDetailChoicePriceById(additionalDetailIds)
        ]);
    }

    /**
     * Determines if price calculation is needed based on the provided DTO.
     *
     * @param dto - Data transfer object containing job details
     * @returns Boolean indicating whether price calculation is needed
     * @private
     */
    private needPriceCalculation(dto: CreateResidentialCleaningJobDto | UpdateResidentialCleaningJobDto): boolean {
        return !!(
            dto.serviceArea ||
            dto.serviceType ||
            dto.serviceFrequency ||
            dto.serviceDetails ||
            dto.extras ||
            dto.additionalDetails
        );
    }

    /**
     * Matches service details from DTO with their corresponding prices from the database
     * @param dtoServiceDetails Array of service details from request with id and amount
     * @param dbServiceDetails Array of service details from database with id and price
     * @returns Array of objects containing amount and price
     */
    private matchOptionAmountWithPrice(
        dtoServiceDetails: { id: string; amount: number }[],
        dbServiceDetails: { id: string; price: number }[]
    ): { amount: number; price: number }[] {
        // Create a price lookup map for O(1) access
        const priceMap = new Map<string, number>();

        // Populate the map with database prices
        dbServiceDetails?.forEach(detail => {
            priceMap.set(detail.id, detail.price);
        });

        // Match and transform
        return dtoServiceDetails?.map(dtoDetail => {
            const price = priceMap.get(dtoDetail.id);

            // Throw error if price not found for requested service detail
            if (price === undefined) {
                this.logger.error(`Price not found for service detail ID: ${dtoDetail.id}`);
            }

            return {
                amount: dtoDetail.amount,
                price: price
            };
        });
    }

    /**
     * Calculates the total price for a job based on various price components.
     *
     * @param serviceTypePrice - Price for the service type
     * @param serviceAreaPrice - Price for the service area
     * @param serviceDetailsPrices - Array of prices for service details
     * @param serviceFreqPrice - Price for the service frequency
     * @param serviceFreqDiscount - Discount for the service frequency
     * @param additionalDetailPrices - Array of prices for additional details
     * @returns The calculated total price
     * @private
     */
    private calculateJobPrice(
        serviceTypePrice: number,
        serviceAreaPrice: number,
        serviceDetailsPrices: {amount: number, price: number}[] = [],
        extrasPrices: {amount: number, price: number}[] = [],
        serviceFreqPrice: number,
        serviceFreqDiscount: number,
        additionalDetailPrices: number[] = []): number {
        return this.priceService.calculateJobPrice({
            serviceTypePrice,
            serviceAreaPrice,
            serviceDetailsPrices,
            extrasPrices,
            serviceFreqPrice,
            serviceFreqDiscount,
            additionalDetailPrices,
            locationTaxes: {
                federal: 0.05,
                state: 0.09975
            }
        })
    }

    /**
     * Creates a response DTO from a scheduled job entity.
     *
     * @param entity - The scheduled job entity to convert to a response DTO
     * @returns ResponseScheduledJobDto containing job details
     * @private
     */
    private createJobResponse({
        scheduledJob,
        jobInfo,
    }: {
        scheduledJob: ScheduledJobDetailsEntity,
        jobInfo?: JobEntity,
    }): ResponseScheduledJobDto {
        if(jobInfo) scheduledJob.job_info = jobInfo;

        return new ScheduledJobDetailsBuilder()
        .fromEntity(scheduledJob)
        .buildDto();
    }

    private parseDate(dateString: string, fieldName: string): Date | undefined {
        if (!dateString) return undefined;
        
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            throw new BaseError(HTTP_ERROR_TYPES.PARSING_ERROR, `Invalid ${fieldName} format: ${dateString}. Expected YYYY-MM-DD`);
        }
        
        return date;
    }
}