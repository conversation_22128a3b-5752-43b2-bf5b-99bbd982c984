// export interface IJobCreator<T> {
//     createJob({
//         dto,
//         jobId,
//         companyId,
//         employeeProfiles,
//         serviceDetails,
//         extras,
//         additionalDetails,
//     }: {
//         dto: T;
//         jobId?: string;
//         companyId?: number;
//         employeeProfiles?: EmployeeProfileEntity[];
//         serviceDetails?: JobAssignedConfigurationEntity[]
//         extras?: JobAssignedExtraEntity[],
//         additionalDetails?: JobAssignedAdditionalDetailsEntity[]
//     }): JobEntity;
// }

// export interface IJobResponse<T> {
//     createJobResponse({
//         entity,
//         totalPrice
//     }: {
//         entity: T
//         totalPrice?: number
//     }): ResponseResidentialCleaningJobDto
// }

// export class JobCreator implements IJobCreator<CreateResidentialCleaningJobDto| UpdateResidentialCleaningJobDto> {
//     createJob({
//         dto,
//         jobId,
//         companyId,
//         employeeProfiles,
//         serviceDetails,
//         extras,
//         additionalDetails,
//       }: {
//         dto: CreateResidentialCleaningJobDto | UpdateResidentialCleaningJobDto;
//         jobId?: string;
//         companyId?: number;
//         employeeProfiles?: EmployeeProfileEntity[];
//         serviceDetails?: JobAssignedConfigurationEntity[];
//         extras?: JobAssignedExtraEntity[];
//         additionalDetails?: JobAssignedAdditionalDetailsEntity[];
//     }): JobEntity {
//         const scheduledJobEntity = new ScheduledJobDetailsBuilder()

//         return new JobBuilder()
//             .setJobId(jobId)
//             .setEntityCompanyId(companyId)
//             .setEntityType(dto.type)
//             .setEntityEmployeeProfiles(employeeProfiles)
//             .setEntityServiceDetails(serviceDetails)
//             .setEntityJobExtras(extras)
//             .setEntityAdditionalDetails(additionalDetails)
//             .fromDto(dto)
//             .buildEntity();
//     }
// }

// export class ResidentialJobResponse implements IJobResponse<JobEntity> {
//     createJobResponse(
//         {
//             entity,
//             totalPrice
//         }: {
//             entity: JobEntity,
//             totalPrice?: number
//         }): ResponseResidentialCleaningJobDto {
//         return new JobBuilder()
//         .setEntityTotalPrice(totalPrice)
//         .fromEntity(entity)
//         .buildDto();
//     }
// }

// export default class JobFactory {
//     createJobEntityCreator<T extends CreateResidentialCleaningJobDto | UpdateResidentialCleaningJobDto> (
//         type: JobType.COMMERCIAL | JobType.RESIDENTIAL, 
//     ): JobCreator<T>{
//         switch (type) {
//             case JobType.COMMERCIAL:
//                 throw new Error(`Unsupported job type: ${type}`);
//             case JobType.RESIDENTIAL:
//                 return new ResidentialJobCreator() as unknown as JobCreator<T>;
//             default:
//                 throw new Error(`Unsupported job type: ${type}`);
//         }
//     }

//     createJobDtoCreator<T extends JobEntity>(
//         type: JobType.COMMERCIAL | JobType.RESIDENTIAL,
//     ): JobResponse<T> {
//         switch (type) {
//             case JobType.COMMERCIAL:
//                 throw new Error(`Unsupported job type: ${type}`);
//             case JobType.RESIDENTIAL:
//                 return new ResidentialJobResponse() as unknown as JobResponse<T>;
//             default:
//                 throw new Error(`Unsupported job type: ${type}`);
//         }
//     }
// }