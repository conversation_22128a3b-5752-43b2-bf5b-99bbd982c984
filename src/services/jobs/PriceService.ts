import { CreateJobPriceDTO, UpdateJobPriceDTO } from "../../dto/v1";

export class PriceService {

    constructor(){}

    calculateJobPrice(job: CreateJobPriceDTO |  UpdateJobPriceDTO): number {
        // Calculate component totals with null coalescing to handle undefined values
        const serviceDetailsTotal = job.serviceDetailsPrices?.reduce((sum, detail) => sum + (detail.price * detail.amount), 0) || 0;
        const extrasTotal = job.extrasPrices?.reduce((sum, extra) => sum + (extra.price * extra.amount), 0) || 0;
        const additionalDetailsTotal = job.additionalDetailPrices?.reduce((sum, price) => sum + price, 0) || 0;

        // Calculate base price and add all components for the subtotal
        const basePrice = job.serviceTypePrice + job.serviceAreaPrice;
        const subtotalBeforeDiscount = basePrice + serviceDetailsTotal + extrasTotal + additionalDetailsTotal;
        
        // First add the frequency price (if any)
        const subtotalWithFreqPrice = job.serviceFreqPrice ? subtotalBeforeDiscount + job.serviceFreqPrice : subtotalBeforeDiscount;

        // Then calculate and apply frequency discount (assuming it's a percentage, e.g. 0.1 for 10%)
        let discountAmount = 0;
        if(job.serviceFreqDiscount){
            discountAmount = subtotalWithFreqPrice * job.serviceFreqDiscount;
        }

        const subtotalAfterDiscount = subtotalWithFreqPrice - discountAmount;

        // Calculate taxes
        const federalTax = subtotalAfterDiscount * ((job.locationTaxes?.federal || 0));
        const stateTax = subtotalAfterDiscount * ((job.locationTaxes?.state || 0));
        const totalTax = federalTax + stateTax;

        // Return rounded to 2 decimal places
        const totalPrice = Math.round((subtotalAfterDiscount + totalTax) * 100) / 100;

        return totalPrice;
    }
}
