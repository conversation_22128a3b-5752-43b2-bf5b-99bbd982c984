FROM node:lts-alpine
ENV NODE_ENV=production
WORKDIR /usr/src/app
# Install bash and other required dependencies for the startup script
RUN apk add --no-cache bash
# Copy package files first for better caching
COPY ["package.json", "./"]
# RUN npm install --production && mv node_modules ../

# Install dependencies
RUN npm install
# Copy the rest of the application
COPY . .
# Create and set permissions for startup script
# COPY start.sh .
# RUN chmod +x start.sh

EXPOSE 3032

# Set ownership to node user
RUN chown -R node /usr/src/app
USER node

# Use startup script instead of direct npm start
CMD ["npm run start"]
