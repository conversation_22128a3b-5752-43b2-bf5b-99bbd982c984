
.frequency-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* Creates 2 equal columns */
    gap: 1rem; /* Space between buttons */
    width: 100%;
    padding: 0.5rem;
    box-sizing: border-box;
}

.frequency-option {
    position: relative;
    width: 100%;
}

.frequency-input {
    position: absolute;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    opacity: 0;
    width: 0;
    height: 0;
}

.frequency-label {
    display: block;
    width: 100%;
    padding: 0.75rem;
    background-color: white;
    cursor: pointer;
    text-align: center;
    font-weight: bold;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-sizing: border-box;
}

.frequency-input:checked + .frequency-label {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
}