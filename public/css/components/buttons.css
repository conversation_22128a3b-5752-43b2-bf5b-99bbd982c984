.action-button {
    background: #28a745;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.action-button:hover {
    background: #218838;
}

.choice-button {
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background-color: white;
    color: rgb(0, 0, 0);
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: inline-block;
    outline: 1px solid #f0f0f0;
    outline-offset: -1px;
    text-align: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.choice-button:hover {
    background: #28a745;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    color: white;
    border-color: #28a745;
    outline-color: #e8e8e8;
}

.choice-button:active {
    background-color: #28a745;
    transform: translateY(0);
}

.choice-button.selected {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
    outline-color: #e8e8e8;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}
