@import 'components/service-frequency.css';
@Import 'components/buttons.css';
@import 'components/form.css';

body {
    font-family: Arial, sans-serif;
    background-color: #f9f9f9;
    color: #333;
}

.form-section {
    background: #fff;
    padding: 10px 10px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 20px auto;
}

.info-message {
    background: #e6f7e6;
    padding: 10px;
    border-radius: 4px;
    color: #2d572c;
}

.form-group {
    margin-top: 20px;
}

.form-group h3, .form-section h2 {
    padding-left: 15px; /* Adjust this value as needed */
    margin-top: 0;
    margin-bottom: 10px; /* Optional, adds spacing below the header */
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Dynamic columns */
    gap: 20px;
    padding: 10px;
}

.form-section > .form-row:first-of-type {
    grid-template-columns: repeat(2, 1fr);
}
.form-field {
    flex: 1;
    padding: 0 10px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

select.material-ui-select, input.material-ui-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: #fafafa;
    font-size: 16px;
    box-sizing: border-box;
}

.material-ui-button {
    padding: 20px 20px;
    border: none;
    border-radius: 4px;
    background-color: #3f51b5;
    color: rgb(0, 0, 0);
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s;
}

small {
    display: block;
    margin-top: 5px;
    color: #888;
}

.frequency-table {
    width: 100%;
    border-spacing: 20px; /* Space between buttons */
}

.section-divider {
    border: 0;
    height: 1px;
    background-color: #ccc; /* Adjust color as needed */
    margin: 20px 0; /* Adjust spacing as needed */
}

@media screen and (max-width: 768px) {
    .form-row, 
    .form-section > .form-row:first-of-type {
        grid-template-columns: 1fr; /* Single column on mobile */
    }

    .frequency-container {
        grid-template-columns: 1fr; /* 1 column on mobile */
    }

    .form-field {
        padding: 0;
    }
}