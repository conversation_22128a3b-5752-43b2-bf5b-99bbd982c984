// Function to initialize all price displays
function initializePriceDisplays() {
  // Set all price displays to $0.00
  document.getElementById('calculator-service-area-choice').textContent = 'Not Selected';
  document.getElementById('calculator-service-type-choice').textContent = 'Not Selected';

  const priceElements = [
    'calculator-price-service-type-number',
    'calculator-price-subtotal-number',
    'calculator-price-federal-tax-number',
    'calculator-price-state-tax-number',
    'calculator-price-total-number'
  ];
  
  priceElements.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
      element.textContent = '$0.00';
    }
  });
}

function formatDateTime(date, time) {
  if (!date || !time) return '';

  const datetime = new Date(`${date}T${time}`);
  
  const isoString = datetime.toISOString().slice(0, -1);
  
  const tzOffset = datetime.getTimezoneOffset();
  const tzHours = Math.abs(Math.floor(tzOffset / 60)).toString().padStart(2, '0');
  const tzMinutes = Math.abs(tzOffset % 60).toString().padStart(2, '0');
  const tzSign = tzOffset > 0 ? '-' : '+';
  
  return `${isoString}${tzSign}${tzHours}:${tzMinutes}`;
}

function formatServiceDetails(serviceDetails) {
  if (!Array.isArray(serviceDetails)) return [];
  

  return serviceDetails.map(item => {
    if (!item || item === "") return null;
    try {
      return JSON.parse(item);
    } catch {
        return null;
    }
  })
  .filter((item) => item !== null);
}

function formatClientData(params){
  return {
    firstName: params["client[firstName]"],
    lastName: params["client[lastName]"],
    phoneNumber: params["client[phoneNumber]"],
    email: params["client[email]"],
    addressInfo: {
      street: params["client[addressInfo][street]"],
      city: params["client[addressInfo][city]"],
      state: params["client[addressInfo][state]"],
      postalCode: params["client[addressInfo][postalCode]"]
    }
  }
}

function handleFormData(event){
  const params = event.detail.parameters;

  return {
    type: params.cleaningType,
    startTime: formatDateTime(params["serviceDate"], params["arrivalTime"]),
    serviceArea: params.serviceArea,
    serviceType: params.serviceType,
    serviceFrequency: params.serviceFrequency,
    status: "PENDING",
    serviceDetails: formatServiceDetails(params["serviceDetails[]"]),
    client: formatClientData(params)
  };
}

function handleServiceAreaCalculations(){
  const serviceAreaSelect = document.querySelector('select[name="serviceArea"]');
  if (serviceAreaSelect) {
      const selectedOption = serviceAreaSelect.options[serviceAreaSelect.selectedIndex];
      const sqFtFrom = selectedOption.dataset.sqftfrom;
      const sqFtTo = selectedOption.dataset.sqftto;
      const serviceAreaPrice = parseFloat(selectedOption.dataset.areaPrice || 0);

      if(serviceAreaSelect.selectedIndex !== 0){
        document.getElementById('calculator-service-area-choice').textContent = `${sqFtFrom} - ${sqFtTo} sq/ft`
        return serviceAreaPrice;
      }
      document.getElementById('calculator-service-area-choice').textContent = `Not Selected`;
      return 0;
  }
}

function handleServiceTypeCalculations(){
  const serviceTypeSelect = document.querySelector('select[name="serviceType"]');
  if(serviceTypeSelect){
    const selectedOption = serviceTypeSelect.options[serviceTypeSelect.selectedIndex];

    if(serviceTypeSelect.selectedIndex !== 0){

      if(selectedOption && selectedOption.dataset.servicePrice){
        const serviceTypePrice = parseFloat(selectedOption.dataset.servicePrice);
    
        document.getElementById('calculator-service-type-choice').textContent = selectedOption.dataset.serviceTypeName;

        document.getElementById('calculator-price-service-type-number').textContent = '$' + serviceTypePrice.toFixed(2);
        return serviceTypePrice;
      }
    }
    document.getElementById('calculator-service-type-choice').textContent = 'Not Selected';
    document.getElementById('calculator-price-service-type-number').textContent = '$0.00';
    return 0;
  }
}

function handleExtrasCalculations() {
  const jobExtrasSelects = document.querySelectorAll('select[name="jobExtras[]"]');
  if(jobExtrasSelects){
    let jobExtraTotal = 0;
    jobExtrasSelects.forEach(select => {
        if (select.value) {
          jobExtraTotal += parseFloat(select.dataset.pricePerExtra || 0);
        }
    });
    return jobExtraTotal;
  }
  return 0;
}

function handleAdditionalDetailsCalculations(){
  const additionalDetailsSelects = document.querySelectorAll('select[name="additionalDetails[]"]');
  if(additionalDetailsSelects){
    let additionalDetailsTotal = 0;
    additionalDetailsSelects.forEach(select => {
      const selectedOption = select.options[select.selectedIndex];

      if(select.selectedIndex > 0 && selectedOption.value) {
        const optionPrice = parseFloat(selectedOption.value);
        if(!isNaN(optionPrice)){
          additionalDetailsTotal += optionPrice;
        }
      }
    })
    return additionalDetailsTotal;
  }
  return 0;
}

function handleFrequencyCalculations(){
  const selectedFrequency = document.querySelector('input[name="serviceFrequency"]:checked');
  if(selectedFrequency){
    const frequencyDiscount = parseFloat(selectedFrequency.dataset.priceFreqDiscount || 0);
    const frequencyPrice = praseFloat(selectedFrequency.dataset.priceFreq || 0);
    if(frequencyPrice){
      return {price: praseFloat(selectedFrequency.dataset.priceFreq || 0), discount: frequencyDiscount};
    }
  }
}

function handleFederalTaxCalculations(round, subtotalPrice){
  const TPS_RATE = 0.05; 
  const taxAmount = round(subtotalPrice * TPS_RATE);
  if(taxAmount){
    document.getElementById('calculator-price-federal-tax-number').textContent = '$' + taxAmount;
    return taxAmount;
  } 
  document.getElementById('calculator-price-federal-tax-number').textContent = '$0.00';
  return 0;
}

function handleStateTaxCalculations(round, subtotalPrice){
  const TVQ_RATE = 0.09975;
  const taxAmount = round(subtotalPrice * TVQ_RATE) 
  if(taxAmount){
    document.getElementById('calculator-price-state-tax-number').textContent = '$' + taxAmount;
    return taxAmount;
  }
  document.getElementById('calculator-price-state-tax-number').textContent = '$0.00';
  return 0;
}

window.calculatePrice = function(){
  let subtotalPrice = 0;
    

  subtotalPrice += handleServiceAreaCalculations();

  subtotalPrice += handleServiceTypeCalculations();
  

  const serviceDetailSelects = document.querySelectorAll('select[name="serviceDetails[]"]');
  serviceDetailSelects.forEach(select => {
      if (select.value) {
        subtotalPrice += parseFloat(select.dataset.pricePerUnit || 0);
      }
  });

  // const frequencyData = handleFrequencyCalculations();
  // subtotalPrice += frequencyData.price

  subtotalPrice += handleExtrasCalculations();

  subtotalPrice += handleAdditionalDetailsCalculations();

  if(subtotalPrice > 0){
    document.getElementById('calculator-price-subtotal-number').textContent = '$'+ subtotalPrice.toFixed(2);
  }else {
    document.getElementById('calculator-price-subtotal-number').textContent = '$0.00';
  }

  // subtotalPrice = subtotalPrice * (frequencyData.discount/100);
  const round = (num) => Math.round(num * 100) / 100;
  
  // Calculate taxes
  const tps = handleFederalTaxCalculations(round, subtotalPrice);
  const tvq = handleStateTaxCalculations(round, subtotalPrice);
  
  // Calculate total
  const totalPrice = round(subtotalPrice + tps + tvq);

  // Update price display
  if(totalPrice > 0){
    document.getElementById('calculator-price-total-number').textContent = '$'+ totalPrice.toFixed(2);
  }else{
    document.getElementById('calculator-price-total-number').textContent = '$0.00';
  }
}


document.addEventListener('htmx:configRequest', (event) => {
  // Only modify parameters for the form submission
  if (event.detail.path === '/api/v1/jobs') {
    event.detail.parameters = handleFormData(event);
  }
});

document.querySelectorAll('.choice-button').forEach(button => {
  button.addEventListener('click', function() {
      // Remove selected class from all buttons
      document.querySelectorAll('.choice-button').forEach(btn => {
          btn.classList.remove('selected');
      });
      
      // Add selected class to clicked button
      this.classList.add('selected');
  });
});

// Set up a global event listener for all HTMX content swaps
document.body.addEventListener('htmx:afterSwap', function(event) {
  // The target property contains the element that was updated
  const target = event.detail.target;
  
  // Check if this is a form swap (you can be more specific with the selector)
  if (target.id === 'formContentArea') {
    initializePriceDisplays();
  }
});

// document.addEventListener('htmx:responseError', function(evt) {
//   const responseDiv = document.getElementById('response-div');
//   responseDiv.innerHTML = `
//     <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4">
//       <strong class="font-bold">Error!</strong>
//       <span class="block sm:inline">${evt.detail.error}</span>
//     </div>
//   `;
// });

htmx.logAll()